'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Terminal as TerminalIcon,
  Server,
  Container,
  Grid3X3,
  Plus,
  Settings,
  Activity,
  Zap,
  Monitor,
  Code,
  Database,
  Cloud,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

import { MultiTerminal } from '@/components/terminal/multi-terminal';
import { SSHTerminal } from '@/components/terminal/ssh-terminal';
import { DockerTerminal } from '@/components/terminal/docker-terminal';

export default function TerminalsPage() {
  const [activeTab, setActiveTab] = useState('multi');

  const quickStartTemplates = [
    {
      id: 'local-dev',
      title: 'Local Development',
      description: 'Standard terminal for local development tasks',
      icon: Code,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      terminals: [
        { type: 'local' as const, title: 'Main Terminal' },
        { type: 'local' as const, title: 'Git Operations' },
      ],
    },
    {
      id: 'docker-workspace',
      title: 'Docker Workspace',
      description: 'Multi-container development environment',
      icon: Container,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
      terminals: [
        { type: 'docker' as const, title: 'Web Container' },
        { type: 'docker' as const, title: 'Database Container' },
        { type: 'local' as const, title: 'Docker Commands' },
      ],
    },
    {
      id: 'remote-servers',
      title: 'Remote Servers',
      description: 'SSH connections to multiple servers',
      icon: Server,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
      terminals: [
        { type: 'ssh' as const, title: 'Production Server' },
        { type: 'ssh' as const, title: 'Staging Server' },
        { type: 'ssh' as const, title: 'Development Server' },
      ],
    },
    {
      id: 'monitoring',
      title: 'System Monitoring',
      description: 'Real-time system monitoring and logs',
      icon: Activity,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20',
      terminals: [
        { type: 'local' as const, title: 'System Logs' },
        { type: 'docker' as const, title: 'App Logs' },
        { type: 'ssh' as const, title: 'Remote Logs' },
      ],
    },
  ];

  const handleQuickStart = (template: typeof quickStartTemplates[0]) => {
    // This would create a new multi-terminal session with the specified configuration
    console.log('Quick start template:', template);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <TerminalIcon className="h-8 w-8" />
            Terminal Dashboard
          </h1>
          <p className="text-muted-foreground mt-2">
            Manage local, remote, and containerized terminal sessions
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            Preferences
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Terminal
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TerminalIcon className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Active Sessions</p>
                <p className="text-2xl font-bold">8</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Server className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium">SSH Connections</p>
                <p className="text-2xl font-bold">3</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Container className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Docker Terminals</p>
                <p className="text-2xl font-bold">5</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium">CPU Usage</p>
                <p className="text-2xl font-bold">24%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Start Templates */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Quick Start Templates</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickStartTemplates.map((template, index) => {
            const Icon = template.icon;
            return (
              <motion.div
                key={template.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="cursor-pointer hover:shadow-md transition-all group">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className={`p-2 rounded-lg ${template.bgColor}`}>
                        <Icon className={`h-6 w-6 ${template.color}`} />
                      </div>
                      <Badge variant="outline">
                        {template.terminals.length} terminals
                      </Badge>
                    </div>
                    <h3 className="font-semibold mb-2">{template.title}</h3>
                    <p className="text-sm text-muted-foreground mb-3">
                      {template.description}
                    </p>
                    <div className="space-y-1 mb-4">
                      {template.terminals.map((terminal, idx) => (
                        <div key={idx} className="flex items-center gap-2 text-xs">
                          <div className={`w-2 h-2 rounded-full ${
                            terminal.type === 'local' ? 'bg-blue-500' :
                            terminal.type === 'ssh' ? 'bg-green-500' : 'bg-purple-500'
                          }`} />
                          <span>{terminal.title}</span>
                        </div>
                      ))}
                    </div>
                    <Button 
                      size="sm" 
                      className="w-full group-hover:bg-primary group-hover:text-primary-foreground"
                      variant="outline"
                      onClick={() => handleQuickStart(template)}
                    >
                      Launch Workspace
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Main Terminal Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="multi" className="flex items-center gap-2">
            <Grid3X3 className="h-4 w-4" />
            Multi Terminal
          </TabsTrigger>
          <TabsTrigger value="ssh" className="flex items-center gap-2">
            <Server className="h-4 w-4" />
            SSH Terminal
          </TabsTrigger>
          <TabsTrigger value="docker" className="flex items-center gap-2">
            <Container className="h-4 w-4" />
            Docker Terminal
          </TabsTrigger>
          <TabsTrigger value="monitoring" className="flex items-center gap-2">
            <Monitor className="h-4 w-4" />
            System Monitor
          </TabsTrigger>
        </TabsList>

        <TabsContent value="multi" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Grid3X3 className="h-5 w-5" />
                Multi Terminal Environment
              </CardTitle>
              <CardDescription>
                Manage multiple terminal sessions with support for tabs, splits, and different connection types
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <MultiTerminal
                height="600px"
                maxTabs={20}
                allowSplit
                allowReorder
                showTabBar
                showToolbar
                defaultTabs={[
                  { type: 'local', title: 'Main Terminal', isPinned: true },
                  { type: 'local', title: 'Git Operations' },
                ]}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ssh" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                SSH Terminal
              </CardTitle>
              <CardDescription>
                Connect to remote servers via SSH and manage them through a web-based terminal
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <SSHTerminal
                height="600px"
                showToolbar
                showStatusBar
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="docker" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Container className="h-5 w-5" />
                Docker Terminal
              </CardTitle>
              <CardDescription>
                Execute commands inside Docker containers with an interactive terminal interface
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <DockerTerminal
                height="600px"
                showToolbar
                showStatusBar
                defaultShell="/bin/bash"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">System Logs</CardTitle>
                <CardDescription>Real-time system log monitoring</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <MultiTerminal
                  height="300px"
                  maxTabs={3}
                  showTabBar={false}
                  showToolbar={false}
                  defaultTabs={[
                    { type: 'local', title: 'System Logs' },
                  ]}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Application Logs</CardTitle>
                <CardDescription>Docker container logs</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <DockerTerminal
                  height="300px"
                  showToolbar={false}
                  showStatusBar={false}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Remote Server Monitoring</CardTitle>
                <CardDescription>SSH-based server monitoring</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <SSHTerminal
                  height="300px"
                  showToolbar={false}
                  showStatusBar={false}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Performance Metrics</CardTitle>
                <CardDescription>Real-time system performance</CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">CPU Usage</span>
                    <Badge>24%</Badge>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '24%' }} />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Memory Usage</span>
                    <Badge>67%</Badge>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{ width: '67%' }} />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Disk Usage</span>
                    <Badge>43%</Badge>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-orange-600 h-2 rounded-full" style={{ width: '43%' }} />
                  </div>

                  <Separator />

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="font-medium">Network I/O</p>
                      <p className="text-muted-foreground">↑ 1.2 MB/s ↓ 3.4 MB/s</p>
                    </div>
                    <div>
                      <p className="font-medium">Disk I/O</p>
                      <p className="text-muted-foreground">↑ 450 KB/s ↓ 1.1 MB/s</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Recent Sessions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Terminal Sessions</CardTitle>
          <CardDescription>
            Quickly reconnect to your recent terminal sessions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              { type: 'ssh', title: 'production-server', time: '2 minutes ago', status: 'active' },
              { type: 'docker', title: 'web-container', time: '5 minutes ago', status: 'active' },
              { type: 'local', title: 'git-operations', time: '12 minutes ago', status: 'inactive' },
              { type: 'ssh', title: 'staging-server', time: '1 hour ago', status: 'inactive' },
            ].map((session, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded ${
                    session.type === 'ssh' ? 'bg-green-100 text-green-600 dark:bg-green-900/20' :
                    session.type === 'docker' ? 'bg-purple-100 text-purple-600 dark:bg-purple-900/20' :
                    'bg-blue-100 text-blue-600 dark:bg-blue-900/20'
                  }`}>
                    {session.type === 'ssh' ? <Server className="h-4 w-4" /> :
                     session.type === 'docker' ? <Container className="h-4 w-4" /> :
                     <TerminalIcon className="h-4 w-4" />}
                  </div>
                  <div>
                    <p className="font-medium">{session.title}</p>
                    <p className="text-sm text-muted-foreground">{session.time}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={session.status === 'active' ? 'default' : 'secondary'}>
                    {session.status}
                  </Badge>
                  <Button size="sm" variant="outline">
                    {session.status === 'active' ? 'View' : 'Reconnect'}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}