'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Server,
  Terminal as TerminalIcon,
  Container,
  Plus,
  Settings,
  Wifi,
  WifiOff,
  Lock,
  Key,
  Activity,
  Clock,
  HardDrive,
  Cpu,
  Network,
  RefreshCw,
  Play,
  Square,
  Trash2,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  FileText,
  Download,
  Upload,
  Database,
  Globe,
  Code,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { MultiTerminal } from '@/components/terminal/multi-terminal';
import { SSHTerminal } from '@/components/terminal/ssh-terminal';
import { DockerTerminal } from '@/components/terminal/docker-terminal';
import { SSHConnectionManager } from '@/components/ssh-docker/ssh-connection-manager';
import { useSSHDocker } from '@/hooks/useSSHDocker';
import { useDocker } from '@/hooks/useDocker';

interface SSHDockerWorkspace {
  id: string;
  name: string;
  description: string;
  sshConnections: string[];
  dockerContainers: string[];
  createdAt: Date;
  lastUsed: Date;
  isActive: boolean;
}

export default function SSHDockerPage() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedWorkspace, setSelectedWorkspace] = useState<string | null>(null);
  const [workspaces, setWorkspaces] = useState<SSHDockerWorkspace[]>([]);
  const [showCreateWorkspace, setShowCreateWorkspace] = useState(false);

  const {
    connections: sshConnections,
    loading: sshLoading,
    error: sshError,
    refreshConnections: refreshSSH,
  } = useSSHDocker();

  const {
    containers,
    connected: dockerConnected,
    loading: dockerLoading,
    error: dockerError,
    refreshContainers: refreshDocker,
  } = useDocker();

  // Initialize with mock workspaces
  useEffect(() => {
    const mockWorkspaces: SSHDockerWorkspace[] = [
      {
        id: 'ws-fullstack',
        name: 'Full Stack Development',
        description: 'Complete development environment with SSH servers and Docker containers',
        sshConnections: ['omnispace-vm'],
        dockerContainers: ['web-app', 'database'],
        createdAt: new Date(Date.now() - 86400000),
        lastUsed: new Date(Date.now() - 3600000),
        isActive: true,
      },
      {
        id: 'ws-microservices',
        name: 'Microservices Architecture',
        description: 'Distributed system with multiple services across SSH and Docker',
        sshConnections: ['api-server', 'auth-server'],
        dockerContainers: ['redis', 'postgres', 'nginx'],
        createdAt: new Date(Date.now() - 172800000),
        lastUsed: new Date(Date.now() - 7200000),
        isActive: false,
      },
    ];
    setWorkspaces(mockWorkspaces);
    setSelectedWorkspace(mockWorkspaces[0].id);
  }, []);

  const getSystemStats = () => {
    return {
      totalSSHConnections: sshConnections.length,
      activeSSHConnections: sshConnections.filter(c => c.connected).length,
      totalContainers: containers.length,
      runningContainers: containers.filter(c => c.status === 'running').length,
      activeWorkspaces: workspaces.filter(w => w.isActive).length,
    };
  };

  const stats = getSystemStats();
  const selectedWorkspaceData = workspaces.find(w => w.id === selectedWorkspace);

  const workspaceTemplates = [
    {
      id: 'dev-ops',
      name: 'DevOps Environment',
      description: 'SSH servers for deployment and Docker for local development',
      icon: Server,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      terminals: [
        { type: 'ssh' as const, title: 'Production Server' },
        { type: 'ssh' as const, title: 'Staging Server' },
        { type: 'docker' as const, title: 'Local Database' },
        { type: 'docker' as const, title: 'Redis Cache' },
      ],
    },
    {
      id: 'full-stack',
      name: 'Full Stack Development',
      description: 'Complete development stack with remote and local services',
      icon: Code,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
      terminals: [
        { type: 'ssh' as const, title: 'Remote API Server' },
        { type: 'docker' as const, title: 'Local Frontend' },
        { type: 'docker' as const, title: 'Database Container' },
        { type: 'ssh' as const, title: 'File Server' },
      ],
    },
    {
      id: 'data-pipeline',
      name: 'Data Pipeline',
      description: 'Data processing with remote servers and containerized tools',
      icon: Database,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
      terminals: [
        { type: 'ssh' as const, title: 'Data Lake Server' },
        { type: 'docker' as const, title: 'Processing Engine' },
        { type: 'docker' as const, title: 'Analytics DB' },
        { type: 'ssh' as const, title: 'ML Training Server' },
      ],
    },
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Server className="h-8 w-8" />
            SSH Docker Integration
          </h1>
          <p className="text-muted-foreground mt-2">
            Unified terminal environment for SSH remote access and Docker container management
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            onClick={() => {
              refreshSSH();
              refreshDocker();
            }}
            disabled={sshLoading || dockerLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${(sshLoading || dockerLoading) ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={() => setShowCreateWorkspace(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Workspace
          </Button>
        </div>
      </div>

      {/* System Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Server className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">SSH Connections</p>
                <p className="text-2xl font-bold">{stats.totalSSHConnections}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Wifi className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium">Active SSH</p>
                <p className="text-2xl font-bold">{stats.activeSSHConnections}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Container className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Containers</p>
                <p className="text-2xl font-bold">{stats.totalContainers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Play className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium">Running</p>
                <p className="text-2xl font-bold">{stats.runningContainers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-cyan-600" />
              <div>
                <p className="text-sm font-medium">Workspaces</p>
                <p className="text-2xl font-bold">{stats.activeWorkspaces}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Error Display */}
      {(sshError || dockerError) && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/10">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              <p className="font-medium">Connection Issues</p>
            </div>
            {sshError && (
              <p className="text-sm text-red-600 mt-1">SSH: {sshError}</p>
            )}
            {dockerError && (
              <p className="text-sm text-red-600 mt-1">Docker: {dockerError}</p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="workspace" className="flex items-center gap-2">
            <TerminalIcon className="h-4 w-4" />
            Workspace
          </TabsTrigger>
          <TabsTrigger value="connections" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Connections
          </TabsTrigger>
          <TabsTrigger value="monitoring" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Monitoring
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          {/* Workspace Templates */}
          <Card>
            <CardHeader>
              <CardTitle>Workspace Templates</CardTitle>
              <CardDescription>
                Pre-configured environments combining SSH and Docker terminals
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {workspaceTemplates.map((template, index) => {
                  const Icon = template.icon;
                  return (
                    <motion.div
                      key={template.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Card className="cursor-pointer hover:shadow-md transition-all group">
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div className={`p-2 rounded-lg ${template.bgColor}`}>
                              <Icon className={`h-6 w-6 ${template.color}`} />
                            </div>
                            <Badge variant="outline">
                              {template.terminals.length} terminals
                            </Badge>
                          </div>
                          <h3 className="font-semibold mb-2">{template.name}</h3>
                          <p className="text-sm text-muted-foreground mb-3">
                            {template.description}
                          </p>
                          <div className="space-y-1 mb-4">
                            {template.terminals.map((terminal, idx) => (
                              <div key={idx} className="flex items-center gap-2 text-xs">
                                <div className={`w-2 h-2 rounded-full ${
                                  terminal.type === 'ssh' ? 'bg-green-500' : 'bg-purple-500'
                                }`} />
                                <span>{terminal.title}</span>
                              </div>
                            ))}
                          </div>
                          <Button 
                            size="sm" 
                            className="w-full group-hover:bg-primary group-hover:text-primary-foreground"
                            variant="outline"
                          >
                            Create Workspace
                          </Button>
                        </CardContent>
                      </Card>
                    </motion.div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Active Workspaces */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Active Workspaces
              </CardTitle>
              <CardDescription>
                Currently configured SSH-Docker workspace environments
              </CardDescription>
            </CardHeader>
            <CardContent>
              {workspaces.length > 0 ? (
                <div className="space-y-3">
                  {workspaces.map((workspace, index) => (
                    <motion.div
                      key={workspace.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={`flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer ${
                        selectedWorkspace === workspace.id ? 'border-primary bg-primary/5' : ''
                      }`}
                      onClick={() => setSelectedWorkspace(workspace.id)}
                    >
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                          <Server className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">{workspace.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {workspace.description}
                          </p>
                          <div className="flex items-center gap-4 mt-1">
                            <span className="text-xs text-muted-foreground flex items-center gap-1">
                              <Server className="h-3 w-3" />
                              {workspace.sshConnections.length} SSH
                            </span>
                            <span className="text-xs text-muted-foreground flex items-center gap-1">
                              <Container className="h-3 w-3" />
                              {workspace.dockerContainers.length} Docker
                            </span>
                            <span className="text-xs text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {workspace.lastUsed.toLocaleTimeString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {workspace.isActive && (
                          <Badge className="bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400">
                            Active
                          </Badge>
                        )}
                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setActiveTab('workspace');
                          }}
                        >
                          Open Workspace
                        </Button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Server className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-lg font-medium mb-2">No Workspaces</p>
                  <p className="text-muted-foreground mb-4">
                    Create your first SSH-Docker workspace
                  </p>
                  <Button onClick={() => setShowCreateWorkspace(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Workspace
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* System Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">SSH Connections Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {sshConnections.slice(0, 5).map((connection, index) => (
                    <div key={connection.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${
                          connection.connected ? 'bg-green-500' : 'bg-red-500'
                        }`} />
                        <span className="text-sm">{connection.id}</span>
                      </div>
                      <Badge variant={connection.connected ? 'default' : 'secondary'}>
                        {connection.connected ? 'Connected' : 'Disconnected'}
                      </Badge>
                    </div>
                  ))}
                  {sshConnections.length === 0 && (
                    <p className="text-sm text-muted-foreground text-center py-4">
                      No SSH connections configured
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Docker Containers Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {containers.slice(0, 5).map((container, index) => (
                    <div key={container.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${
                          container.status === 'running' ? 'bg-green-500' : 'bg-red-500'
                        }`} />
                        <span className="text-sm">{container.name}</span>
                      </div>
                      <Badge variant={container.status === 'running' ? 'default' : 'secondary'}>
                        {container.status}
                      </Badge>
                    </div>
                  ))}
                  {containers.length === 0 && (
                    <p className="text-sm text-muted-foreground text-center py-4">
                      {dockerConnected ? 'No containers available' : 'Docker not connected'}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="workspace" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <TerminalIcon className="h-5 w-5" />
                    SSH-Docker Workspace
                  </CardTitle>
                  <CardDescription>
                    {selectedWorkspaceData?.description || 'Unified terminal environment'}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Select value={selectedWorkspace || ''} onValueChange={setSelectedWorkspace}>
                    <SelectTrigger className="w-64">
                      <SelectValue placeholder="Select workspace..." />
                    </SelectTrigger>
                    <SelectContent>
                      {workspaces.map((workspace) => (
                        <SelectItem key={workspace.id} value={workspace.id}>
                          {workspace.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <MultiTerminal
                height="600px"
                maxTabs={15}
                allowSplit
                allowReorder
                showTabBar
                showToolbar
                defaultTabs={
                  selectedWorkspaceData ? [
                    { type: 'ssh', title: 'SSH Server', isPinned: true },
                    { type: 'docker', title: 'Docker Container' },
                    { type: 'local', title: 'Local Terminal' },
                  ] : [
                    { type: 'local', title: 'Terminal', isPinned: true },
                  ]
                }
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="connections" className="space-y-4">
          <SSHConnectionManager />
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">SSH Connection Logs</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <SSHTerminal
                  height="300px"
                  showToolbar={false}
                  showStatusBar={false}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Docker Container Logs</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <DockerTerminal
                  height="300px"
                  showToolbar={false}
                  showStatusBar={false}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">System Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">SSH Connections</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ width: `${(stats.activeSSHConnections / Math.max(stats.totalSSHConnections, 1)) * 100}%` }} 
                        />
                      </div>
                      <Badge>{stats.activeSSHConnections}/{stats.totalSSHConnections}</Badge>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Running Containers</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${(stats.runningContainers / Math.max(stats.totalContainers, 1)) * 100}%` }} 
                        />
                      </div>
                      <Badge>{stats.runningContainers}/{stats.totalContainers}</Badge>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Active Workspaces</span>
                    <Badge variant="outline">{stats.activeWorkspaces}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Resource Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Network I/O</span>
                    <span>↑ 2.1 MB/s ↓ 5.3 MB/s</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>SSH Sessions</span>
                    <span>{stats.activeSSHConnections} active</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Docker Memory</span>
                    <span>1.2 GB / 4.0 GB</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Terminal Sessions</span>
                    <span>6 active</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}