'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Terminal as TerminalIcon,
  Server,
  Container,
  Grid3X3,
  Plus,
  Settings,
  Activity,
  Zap,
  Monitor,
  Code,
  Database,
  Cloud,
  Play,
  Pause,
  Square,
  RotateCcw,
  Trash2,
  Eye,
  EyeOff,
  Maximize2,
  Minimize2,
  Layout,
  Layers,
  Command,
  GitBranch,
  FileText,
  Cpu,
  HardDrive,
  Network,
  Clock,
  Wifi,
  WifiOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  Save,
  FolderOpen,
  Download,
  Upload,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { MultiTerminal } from '@/components/terminal/multi-terminal';
import { SSHTerminal } from '@/components/terminal/ssh-terminal';
import { DockerTerminal } from '@/components/terminal/docker-terminal';
import { useDocker } from '@/hooks/useDocker';
import { useSSHDocker } from '@/hooks/useSSHDocker';

interface WorkspaceTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  bgColor: string;
  terminals: Array<{
    type: 'local' | 'ssh' | 'docker';
    title: string;
    config?: any;
  }>;
  layout: 'tabs' | 'grid' | 'horizontal' | 'vertical';
}

interface WorkspaceSession {
  id: string;
  name: string;
  template: string;
  createdAt: Date;
  lastUsed: Date;
  isActive: boolean;
  terminals: number;
}

export default function TerminalWorkspacePage() {
  const [activeWorkspace, setActiveWorkspace] = useState<string | null>(null);
  const [workspaces, setWorkspaces] = useState<WorkspaceSession[]>([]);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [workspaceName, setWorkspaceName] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [autoSave, setAutoSave] = useState(true);
  const [notifications, setNotifications] = useState(true);

  const { containers, connected: dockerConnected } = useDocker();
  const { connections } = useSSHDocker();

  const workspaceTemplates: WorkspaceTemplate[] = [
    {
      id: 'fullstack-dev',
      name: 'Full-Stack Development',
      description: 'Complete development environment with database, API, and frontend terminals',
      icon: Code,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      layout: 'grid',
      terminals: [
        { type: 'local', title: 'API Server', config: { shell: '/bin/bash' } },
        { type: 'local', title: 'Frontend Dev', config: { shell: '/bin/bash' } },
        { type: 'docker', title: 'Database', config: { shell: '/bin/bash' } },
        { type: 'local', title: 'Git Operations', config: { shell: '/bin/bash' } },
      ],
    },
    {
      id: 'devops-monitoring',
      name: 'DevOps Monitoring',
      description: 'Infrastructure monitoring with logs, metrics, and server access',
      icon: Activity,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
      layout: 'vertical',
      terminals: [
        { type: 'ssh', title: 'Production Server', config: {} },
        { type: 'ssh', title: 'Staging Server', config: {} },
        { type: 'local', title: 'Monitoring Tools', config: {} },
        { type: 'docker', title: 'Log Aggregator', config: {} },
      ],
    },
    {
      id: 'microservices',
      name: 'Microservices Environment',
      description: 'Multi-container development with service terminals',
      icon: Container,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
      layout: 'tabs',
      terminals: [
        { type: 'docker', title: 'Auth Service', config: {} },
        { type: 'docker', title: 'API Gateway', config: {} },
        { type: 'docker', title: 'User Service', config: {} },
        { type: 'docker', title: 'Payment Service', config: {} },
        { type: 'local', title: 'Service Mesh', config: {} },
      ],
    },
    {
      id: 'data-science',
      name: 'Data Science Workspace',
      description: 'Python, Jupyter, and database terminals for data work',
      icon: Database,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20',
      layout: 'horizontal',
      terminals: [
        { type: 'docker', title: 'Jupyter Lab', config: { shell: '/bin/bash' } },
        { type: 'docker', title: 'Python Runtime', config: { shell: '/bin/python' } },
        { type: 'docker', title: 'Database', config: { shell: '/bin/bash' } },
      ],
    },
    {
      id: 'cloud-deployment',
      name: 'Cloud Deployment',
      description: 'Multi-cloud deployment and management terminals',
      icon: Cloud,
      color: 'text-cyan-600',
      bgColor: 'bg-cyan-100 dark:bg-cyan-900/20',
      layout: 'grid',
      terminals: [
        { type: 'local', title: 'AWS CLI', config: {} },
        { type: 'local', title: 'Kubernetes', config: {} },
        { type: 'ssh', title: 'Cloud Server', config: {} },
        { type: 'local', title: 'Terraform', config: {} },
      ],
    },
    {
      id: 'debugging-session',
      name: 'Debugging Session',
      description: 'Focused debugging environment with logs and testing',
      icon: Monitor,
      color: 'text-red-600',
      bgColor: 'bg-red-100 dark:bg-red-900/20',
      layout: 'vertical',
      terminals: [
        { type: 'local', title: 'Debug Session', config: {} },
        { type: 'docker', title: 'Application Logs', config: {} },
        { type: 'local', title: 'Test Runner', config: {} },
      ],
    },
  ];

  // Initialize with mock workspaces
  useEffect(() => {
    const mockWorkspaces: WorkspaceSession[] = [
      {
        id: 'ws-1',
        name: 'Main Development',
        template: 'fullstack-dev',
        createdAt: new Date(Date.now() - 86400000),
        lastUsed: new Date(Date.now() - 3600000),
        isActive: true,
        terminals: 4,
      },
      {
        id: 'ws-2',
        name: 'Production Monitoring',
        template: 'devops-monitoring',
        createdAt: new Date(Date.now() - 172800000),
        lastUsed: new Date(Date.now() - 7200000),
        isActive: false,
        terminals: 3,
      },
    ];
    setWorkspaces(mockWorkspaces);
    setActiveWorkspace(mockWorkspaces[0].id);
  }, []);

  const handleCreateWorkspace = () => {
    if (!selectedTemplate || !workspaceName.trim()) return;

    const template = workspaceTemplates.find(t => t.id === selectedTemplate);
    if (!template) return;

    const newWorkspace: WorkspaceSession = {
      id: `ws-${Date.now()}`,
      name: workspaceName.trim(),
      template: selectedTemplate,
      createdAt: new Date(),
      lastUsed: new Date(),
      isActive: true,
      terminals: template.terminals.length,
    };

    setWorkspaces(prev => prev.map(w => ({ ...w, isActive: false })).concat(newWorkspace));
    setActiveWorkspace(newWorkspace.id);
    setShowCreateDialog(false);
    setWorkspaceName('');
    setSelectedTemplate('');
  };

  const handleDeleteWorkspace = (workspaceId: string) => {
    setWorkspaces(prev => prev.filter(w => w.id !== workspaceId));
    if (activeWorkspace === workspaceId) {
      const remaining = workspaces.filter(w => w.id !== workspaceId);
      setActiveWorkspace(remaining[0]?.id || null);
    }
  };

  const handleSaveWorkspace = () => {
    // Implementation for saving workspace state
    console.log('Saving workspace...');
  };

  const handleLoadWorkspace = () => {
    // Implementation for loading workspace state
    console.log('Loading workspace...');
  };

  const getSystemStats = () => {
    return {
      totalContainers: containers.length,
      runningContainers: containers.filter(c => c.status === 'running').length,
      sshConnections: connections.filter(c => c.connected).length,
      activeWorkspaces: workspaces.filter(w => w.isActive).length,
    };
  };

  const stats = getSystemStats();
  const activeWorkspaceData = workspaces.find(w => w.id === activeWorkspace);
  const activeTemplate = activeWorkspaceData ? workspaceTemplates.find(t => t.id === activeWorkspaceData.template) : null;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Layout className="h-8 w-8" />
            Terminal Workspace
          </h1>
          <p className="text-muted-foreground mt-2">
            Comprehensive terminal environment management with workspace templates and multi-session support
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleLoadWorkspace}>
            <FolderOpen className="h-4 w-4 mr-2" />
            Load
          </Button>
          <Button variant="outline" onClick={handleSaveWorkspace}>
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Workspace
          </Button>
        </div>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Layout className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Active Workspaces</p>
                <p className="text-2xl font-bold">{stats.activeWorkspaces}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Container className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium">Running Containers</p>
                <p className="text-2xl font-bold">{stats.runningContainers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Server className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium">SSH Connections</p>
                <p className="text-2xl font-bold">{stats.sshConnections}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TerminalIcon className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium">Total Terminals</p>
                <p className="text-2xl font-bold">
                  {workspaces.reduce((sum, w) => sum + w.terminals, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="space-y-4">
          {/* Active Workspaces */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Active Workspaces</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {workspaces.map((workspace) => {
                    const template = workspaceTemplates.find(t => t.id === workspace.template);
                    const Icon = template?.icon || Layout;
                    
                    return (
                      <div
                        key={workspace.id}
                        className={`p-3 rounded-lg border cursor-pointer transition-all ${
                          activeWorkspace === workspace.id
                            ? 'bg-primary/10 border-primary'
                            : 'hover:bg-muted/50'
                        }`}
                        onClick={() => setActiveWorkspace(workspace.id)}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <div className={`p-1 rounded ${template?.bgColor || 'bg-gray-100'}`}>
                            <Icon className={`h-4 w-4 ${template?.color || 'text-gray-600'}`} />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{workspace.name}</p>
                            <p className="text-xs text-muted-foreground">{template?.name}</p>
                          </div>
                          {workspace.isActive && (
                            <Badge variant="outline" className="text-xs">Active</Badge>
                          )}
                        </div>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>{workspace.terminals} terminals</span>
                          <span>{workspace.lastUsed.toLocaleTimeString()}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Workspace Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Auto-save</label>
                <Switch checked={autoSave} onCheckedChange={setAutoSave} />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Notifications</label>
                <Switch checked={notifications} onCheckedChange={setNotifications} />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Fullscreen</label>
                <Switch checked={isFullscreen} onCheckedChange={setIsFullscreen} />
              </div>
              <Separator />
              <div>
                <label className="text-sm font-medium mb-2 block">Performance</label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span>Buffer Size</span>
                    <span>10K lines</span>
                  </div>
                  <Slider defaultValue={[10]} max={50} step={5} className="h-1" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button size="sm" variant="outline" className="w-full justify-start">
                  <Download className="h-4 w-4 mr-2" />
                  Export Workspace
                </Button>
                <Button size="sm" variant="outline" className="w-full justify-start">
                  <Upload className="h-4 w-4 mr-2" />
                  Import Workspace
                </Button>
                <Button size="sm" variant="outline" className="w-full justify-start">
                  <GitBranch className="h-4 w-4 mr-2" />
                  Template Gallery
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Terminal Area */}
        <div className="lg:col-span-3">
          <Card className="h-[800px]">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {activeTemplate && (
                    <>
                      <div className={`p-2 rounded ${activeTemplate.bgColor}`}>
                        <activeTemplate.icon className={`h-5 w-5 ${activeTemplate.color}`} />
                      </div>
                      <div>
                        <CardTitle className="text-base">
                          {activeWorkspaceData?.name || 'No Workspace Selected'}
                        </CardTitle>
                        <CardDescription>
                          {activeTemplate.description}
                        </CardDescription>
                      </div>
                    </>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    {activeTemplate?.terminals.length || 0} terminals
                  </Badge>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="icon">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>Save Workspace</DropdownMenuItem>
                      <DropdownMenuItem>Duplicate Workspace</DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">
                        Delete Workspace
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0 h-[calc(100%-80px)]">
              {activeTemplate ? (
                <MultiTerminal
                  height="100%"
                  maxTabs={20}
                  allowSplit
                  allowReorder
                  showTabBar
                  showToolbar
                  defaultTabs={activeTemplate.terminals.map((terminal, index) => ({
                    type: terminal.type,
                    title: terminal.title,
                    config: terminal.config,
                    isPinned: index === 0,
                  }))}
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <Layout className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <p className="text-lg font-medium mb-2">No Workspace Selected</p>
                    <p className="text-muted-foreground mb-4">
                      Select a workspace from the sidebar or create a new one
                    </p>
                    <Button onClick={() => setShowCreateDialog(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Workspace
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Create Workspace Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Workspace</DialogTitle>
            <DialogDescription>
              Choose a template and customize your terminal workspace
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Workspace Name */}
            <div>
              <label className="text-sm font-medium mb-2 block">Workspace Name</label>
              <input
                type="text"
                placeholder="My Development Workspace"
                value={workspaceName}
                onChange={(e) => setWorkspaceName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Template Selection */}
            <div>
              <label className="text-sm font-medium mb-3 block">Choose Template</label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {workspaceTemplates.map((template) => {
                  const Icon = template.icon;
                  return (
                    <div
                      key={template.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        selectedTemplate === template.id
                          ? 'border-primary bg-primary/5'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedTemplate(template.id)}
                    >
                      <div className="flex items-start gap-3">
                        <div className={`p-2 rounded ${template.bgColor}`}>
                          <Icon className={`h-5 w-5 ${template.color}`} />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold mb-1">{template.name}</h3>
                          <p className="text-sm text-muted-foreground mb-3">
                            {template.description}
                          </p>
                          <div className="space-y-1">
                            <div className="flex items-center justify-between text-xs">
                              <span>Layout:</span>
                              <Badge variant="outline" className="text-xs">
                                {template.layout}
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between text-xs">
                              <span>Terminals:</span>
                              <span>{template.terminals.length}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="mt-3 space-y-1">
                        {template.terminals.map((terminal, index) => (
                          <div key={index} className="flex items-center gap-2 text-xs">
                            <div className={`w-2 h-2 rounded-full ${
                              terminal.type === 'local' ? 'bg-blue-500' :
                              terminal.type === 'ssh' ? 'bg-green-500' : 'bg-purple-500'
                            }`} />
                            <span>{terminal.title}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Preview */}
            {selectedTemplate && (
              <div>
                <label className="text-sm font-medium mb-2 block">Preview</label>
                <div className="border rounded-lg p-4 bg-muted/20">
                  {(() => {
                    const template = workspaceTemplates.find(t => t.id === selectedTemplate);
                    if (!template) return null;
                    
                    return (
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <template.icon className={`h-4 w-4 ${template.color}`} />
                          <span className="font-medium">{template.name}</span>
                          <Badge variant="outline">{template.layout} layout</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {template.description}
                        </p>
                        <div className="grid grid-cols-2 gap-2">
                          {template.terminals.map((terminal, index) => (
                            <div key={index} className="flex items-center gap-2 text-sm p-2 bg-background rounded border">
                              <div className={`w-3 h-3 rounded ${
                                terminal.type === 'local' ? 'bg-blue-500' :
                                terminal.type === 'ssh' ? 'bg-green-500' : 'bg-purple-500'
                              }`} />
                              <span>{terminal.title}</span>
                              <Badge variant="outline" className="text-xs ml-auto">
                                {terminal.type}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>
            )}
          </div>

          <div className="flex gap-2 justify-end pt-4">
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleCreateWorkspace}
              disabled={!selectedTemplate || !workspaceName.trim()}
            >
              Create Workspace
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}