'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Server,
  Terminal as TerminalIcon,
  Plus,
  Settings,
  Wifi,
  WifiOff,
  Lock,
  Key,
  Activity,
  Clock,
  HardDrive,
  Cpu,
  Network,
  RefreshCw,
  Play,
  Square,
  Trash2,
  Eye,
  EyeOff,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';

import { SSHTerminal } from '@/components/terminal/ssh-terminal';
import { MultiTerminal } from '@/components/terminal/multi-terminal';
import { SSHConnectionManager } from '@/components/ssh-docker/ssh-connection-manager';
import { useSSHDocker } from '@/hooks/useSSHDocker';
import { SSHConnectionConfig, SSHConnectionStatus } from '@/types/ssh-docker';

interface ActiveSession {
  id: string;
  connectionId: string;
  connectionName: string;
  host: string;
  username: string;
  startedAt: Date;
  isActive: boolean;
}

export default function SSHTerminalPage() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedConnectionId, setSelectedConnectionId] = useState<string | null>(null);
  const [activeSessions, setActiveSessions] = useState<ActiveSession[]>([]);
  const [showConnectionManager, setShowConnectionManager] = useState(false);

  const {
    connections,
    loading,
    error,
    refreshConnections,
  } = useSSHDocker();

  // Update active sessions based on connections
  useEffect(() => {
    const connectedSessions: ActiveSession[] = connections
      .filter(conn => conn.connected)
      .map(conn => ({
        id: `session-${conn.id}`,
        connectionId: conn.id,
        connectionName: conn.id, // Would typically have a proper name
        host: conn.id.split('@')[1]?.split(':')[0] || conn.id,
        username: conn.id.split('@')[0] || 'unknown',
        startedAt: conn.lastConnected || new Date(),
        isActive: true,
      }));
    
    setActiveSessions(connectedSessions);
  }, [connections]);

  const handleConnectionSelect = (connectionId: string) => {
    setSelectedConnectionId(connectionId);
    setActiveTab('terminal');
  };

  const handleNewConnection = () => {
    setShowConnectionManager(true);
    setActiveTab('connections');
  };

  const connectionStats = {
    total: connections.length,
    connected: connections.filter(c => c.connected).length,
    failed: connections.filter(c => c.error).length,
    idle: connections.filter(c => !c.connected && !c.error).length,
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Server className="h-8 w-8" />
            SSH Terminal Dashboard
          </h1>
          <p className="text-muted-foreground mt-2">
            Secure shell access to remote servers with advanced terminal features
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={refreshConnections} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleNewConnection}>
            <Plus className="h-4 w-4 mr-2" />
            New Connection
          </Button>
        </div>
      </div>

      {/* Connection Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Server className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Total Connections</p>
                <p className="text-2xl font-bold">{connectionStats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Wifi className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium">Connected</p>
                <p className="text-2xl font-bold">{connectionStats.connected}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <WifiOff className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm font-medium">Failed</p>
                <p className="text-2xl font-bold">{connectionStats.failed}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Active Sessions</p>
                <p className="text-2xl font-bold">{activeSessions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/10">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-600">
              <WifiOff className="h-5 w-5" />
              <p className="font-medium">Connection Error</p>
            </div>
            <p className="text-sm text-red-600 mt-1">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="terminal" className="flex items-center gap-2">
            <TerminalIcon className="h-4 w-4" />
            Terminal
          </TabsTrigger>
          <TabsTrigger value="multi" className="flex items-center gap-2">
            <Server className="h-4 w-4" />
            Multi-Session
          </TabsTrigger>
          <TabsTrigger value="connections" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Connections
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          {/* Active Sessions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Active SSH Sessions
              </CardTitle>
              <CardDescription>
                Currently connected SSH terminal sessions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {activeSessions.length > 0 ? (
                <div className="space-y-3">
                  {activeSessions.map((session, index) => (
                    <motion.div
                      key={session.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                          <Server className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium">{session.connectionName}</p>
                          <p className="text-sm text-muted-foreground">
                            {session.username}@{session.host}
                          </p>
                          <div className="flex items-center gap-4 mt-1">
                            <span className="text-xs text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              Started {session.startedAt.toLocaleTimeString()}
                            </span>
                            <Badge variant="outline" className="text-xs">
                              <Lock className="h-3 w-3 mr-1" />
                              Encrypted
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400">
                          Active
                        </Badge>
                        <Button
                          size="sm"
                          onClick={() => handleConnectionSelect(session.connectionId)}
                        >
                          Open Terminal
                        </Button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Server className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-lg font-medium mb-2">No Active Sessions</p>
                  <p className="text-muted-foreground mb-4">
                    Connect to a server to start your first SSH session
                  </p>
                  <Button onClick={handleNewConnection}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Connection
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="cursor-pointer hover:shadow-md transition-all">
              <CardContent className="p-6 text-center">
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-full w-fit mx-auto mb-4">
                  <TerminalIcon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Quick Terminal</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Start a single SSH terminal session
                </p>
                <Button size="sm" onClick={() => setActiveTab('terminal')}>
                  Launch Terminal
                </Button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-all">
              <CardContent className="p-6 text-center">
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-full w-fit mx-auto mb-4">
                  <Server className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Multi-Session</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Manage multiple SSH connections
                </p>
                <Button size="sm" onClick={() => setActiveTab('multi')}>
                  Open Multi-Terminal
                </Button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-all">
              <CardContent className="p-6 text-center">
                <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-full w-fit mx-auto mb-4">
                  <Settings className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Manage Connections</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Configure SSH connection settings
                </p>
                <Button size="sm" onClick={() => setActiveTab('connections')}>
                  Manage Connections
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Recent Connections */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Connections</CardTitle>
              <CardDescription>
                Recently used SSH connections
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {connections.slice(0, 10).map((connection, index) => (
                    <div
                      key={connection.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className={`p-1 rounded ${
                          connection.connected
                            ? 'bg-green-100 text-green-600 dark:bg-green-900/20'
                            : connection.error
                            ? 'bg-red-100 text-red-600 dark:bg-red-900/20'
                            : 'bg-gray-100 text-gray-600 dark:bg-gray-900/20'
                        }`}>
                          {connection.connected ? (
                            <Wifi className="h-4 w-4" />
                          ) : (
                            <WifiOff className="h-4 w-4" />
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium">{connection.id}</p>
                          {connection.lastConnected && (
                            <p className="text-xs text-muted-foreground">
                              Last connected: {connection.lastConnected.toLocaleString()}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            connection.connected
                              ? 'default'
                              : connection.error
                              ? 'destructive'
                              : 'secondary'
                          }
                        >
                          {connection.connected
                            ? 'Connected'
                            : connection.error
                            ? 'Error'
                            : 'Idle'}
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleConnectionSelect(connection.id)}
                        >
                          Connect
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="terminal" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TerminalIcon className="h-5 w-5" />
                SSH Terminal
              </CardTitle>
              <CardDescription>
                Secure shell terminal with full XTerm.js features
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <SSHTerminal
                connectionId={selectedConnectionId || undefined}
                height="600px"
                showToolbar
                showStatusBar
                autoConnect={!!selectedConnectionId}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="multi" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                Multi-Session SSH Terminal
              </CardTitle>
              <CardDescription>
                Manage multiple SSH connections in tabs, splits, or grid layout
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <MultiTerminal
                height="600px"
                maxTabs={15}
                allowSplit
                allowReorder
                showTabBar
                showToolbar
                defaultTabs={[
                  {
                    type: 'ssh',
                    title: 'SSH Terminal',
                    config: selectedConnectionId ? { connectionId: selectedConnectionId } : undefined,
                  },
                ]}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="connections" className="space-y-4">
          <SSHConnectionManager
            onConnectionSelect={handleConnectionSelect}
            selectedConnectionId={selectedConnectionId || undefined}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}