'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Container,
  Terminal as TerminalIcon,
  Play,
  Square,
  Pause,
  RotateCcw,
  Trash2,
  Plus,
  Settings,
  Activity,
  HardDrive,
  Cpu,
  Network,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  Eye,
  Code,
  Database,
  Globe,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { DockerTerminal } from '@/components/terminal/docker-terminal';
import { MultiTerminal } from '@/components/terminal/multi-terminal';
import { useDocker } from '@/hooks/useDocker';
import { ContainerInfo } from '@/types/docker';

interface ContainerSession {
  id: string;
  containerId: string;
  containerName: string;
  shell: string;
  startedAt: Date;
  isActive: boolean;
}

export default function DockerTerminalPage() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedContainerId, setSelectedContainerId] = useState<string | null>(null);
  const [containerSessions, setContainerSessions] = useState<ContainerSession[]>([]);
  const [selectedShell, setSelectedShell] = useState('/bin/bash');

  const {
    containers,
    images,
    loading,
    error,
    connected,
    refreshContainers,
    startContainer,
    stopContainer,
    removeContainer,
  } = useDocker();

  const runningContainers = containers.filter(c => c.status === 'running');
  const stoppedContainers = containers.filter(c => c.status === 'stopped');

  // Handle container selection
  const handleContainerSelect = (containerId: string) => {
    const container = containers.find(c => c.id === containerId);
    if (container && container.status === 'running') {
      setSelectedContainerId(containerId);
      setActiveTab('terminal');
    }
  };

  // Handle container actions
  const handleContainerAction = async (containerId: string, action: string) => {
    try {
      switch (action) {
        case 'start':
          await startContainer(containerId);
          break;
        case 'stop':
          await stopContainer(containerId);
          break;
        case 'remove':
          await removeContainer(containerId);
          break;
      }
    } catch (error) {
      console.error(`Failed to ${action} container:`, error);
    }
  };

  // Get container status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'stopped': return <XCircle className="h-4 w-4 text-red-600" />;
      case 'starting': return <Loader2 className="h-4 w-4 animate-spin text-yellow-600" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  // Get container type icon based on image
  const getContainerTypeIcon = (image: string) => {
    if (image.includes('postgres') || image.includes('mysql') || image.includes('mongo')) {
      return <Database className="h-4 w-4" />;
    }
    if (image.includes('nginx') || image.includes('apache') || image.includes('node')) {
      return <Globe className="h-4 w-4" />;
    }
    return <Container className="h-4 w-4" />;
  };

  const containerStats = {
    total: containers.length,
    running: runningContainers.length,
    stopped: stoppedContainers.length,
    images: images.length,
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Container className="h-8 w-8" />
            Docker Terminal Dashboard
          </h1>
          <p className="text-muted-foreground mt-2">
            Interactive terminal access to Docker containers with advanced features
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={refreshContainers} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Container
          </Button>
        </div>
      </div>

      {/* Docker Status */}
      {!connected && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/10">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-600">
              <XCircle className="h-5 w-5" />
              <p className="font-medium">Docker Not Connected</p>
            </div>
            <p className="text-sm text-red-600 mt-1">
              Cannot connect to Docker daemon. Please ensure Docker is running.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Container Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Container className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Total Containers</p>
                <p className="text-2xl font-bold">{containerStats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Play className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium">Running</p>
                <p className="text-2xl font-bold">{containerStats.running}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Square className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm font-medium">Stopped</p>
                <p className="text-2xl font-bold">{containerStats.stopped}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <HardDrive className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Images</p>
                <p className="text-2xl font-bold">{containerStats.images}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/10">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              <p className="font-medium">Docker Error</p>
            </div>
            <p className="text-sm text-red-600 mt-1">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="terminal" className="flex items-center gap-2">
            <TerminalIcon className="h-4 w-4" />
            Terminal
          </TabsTrigger>
          <TabsTrigger value="multi" className="flex items-center gap-2">
            <Container className="h-4 w-4" />
            Multi-Container
          </TabsTrigger>
          <TabsTrigger value="monitoring" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Monitoring
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          {/* Running Containers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5 text-green-600" />
                Running Containers
              </CardTitle>
              <CardDescription>
                Containers available for terminal access
              </CardDescription>
            </CardHeader>
            <CardContent>
              {runningContainers.length > 0 ? (
                <div className="space-y-3">
                  {runningContainers.map((container, index) => (
                    <motion.div
                      key={container.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                          {getContainerTypeIcon(container.image)}
                        </div>
                        <div>
                          <p className="font-medium">{container.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {container.image}
                          </p>
                          <div className="flex items-center gap-4 mt-1">
                            <span className="text-xs text-muted-foreground flex items-center gap-1">
                              {getStatusIcon(container.status)}
                              {container.status}
                            </span>
                            {container.cpu !== undefined && (
                              <span className="text-xs text-muted-foreground flex items-center gap-1">
                                <Cpu className="h-3 w-3" />
                                {container.cpu}%
                              </span>
                            )}
                            {container.memory !== undefined && (
                              <span className="text-xs text-muted-foreground flex items-center gap-1">
                                <HardDrive className="h-3 w-3" />
                                {container.memory}MB
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400">
                          Running
                        </Badge>
                        <Button
                          size="sm"
                          onClick={() => handleContainerSelect(container.id)}
                        >
                          <TerminalIcon className="h-4 w-4 mr-2" />
                          Open Terminal
                        </Button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Container className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-lg font-medium mb-2">No Running Containers</p>
                  <p className="text-muted-foreground mb-4">
                    Start a container to access its terminal
                  </p>
                  {stoppedContainers.length > 0 && (
                    <p className="text-sm text-muted-foreground">
                      {stoppedContainers.length} stopped container{stoppedContainers.length !== 1 ? 's' : ''} available to start
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="cursor-pointer hover:shadow-md transition-all">
              <CardContent className="p-6 text-center">
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-full w-fit mx-auto mb-4">
                  <TerminalIcon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Single Terminal</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Connect to one container terminal
                </p>
                <Button size="sm" onClick={() => setActiveTab('terminal')}>
                  Launch Terminal
                </Button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-all">
              <CardContent className="p-6 text-center">
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-full w-fit mx-auto mb-4">
                  <Container className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Multi-Container</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Manage multiple container terminals
                </p>
                <Button size="sm" onClick={() => setActiveTab('multi')}>
                  Open Multi-Terminal
                </Button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-all">
              <CardContent className="p-6 text-center">
                <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-full w-fit mx-auto mb-4">
                  <Eye className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Container Monitoring</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Real-time container monitoring
                </p>
                <Button size="sm" onClick={() => setActiveTab('monitoring')}>
                  View Monitoring
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* All Containers */}
          <Card>
            <CardHeader>
              <CardTitle>All Containers</CardTitle>
              <CardDescription>
                Complete list of Docker containers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {containers.map((container, index) => (
                    <div
                      key={container.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-1 rounded">
                          {getContainerTypeIcon(container.image)}
                        </div>
                        <div>
                          <p className="text-sm font-medium">{container.name}</p>
                          <p className="text-xs text-muted-foreground">{container.image}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            container.status === 'running'
                              ? 'default'
                              : container.status === 'stopped'
                              ? 'secondary'
                              : 'destructive'
                          }
                        >
                          {container.status}
                        </Badge>
                        <div className="flex gap-1">
                          {container.status === 'running' ? (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleContainerSelect(container.id)}
                              >
                                <TerminalIcon className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleContainerAction(container.id, 'stop')}
                              >
                                <Square className="h-3 w-3" />
                              </Button>
                            </>
                          ) : (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleContainerAction(container.id, 'start')}
                            >
                              <Play className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="terminal" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TerminalIcon className="h-5 w-5" />
                Docker Container Terminal
              </CardTitle>
              <CardDescription>
                Interactive terminal access to Docker containers
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <DockerTerminal
                containerId={selectedContainerId || undefined}
                height="600px"
                showToolbar
                showStatusBar
                autoConnect={!!selectedContainerId}
                defaultShell={selectedShell}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="multi" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Container className="h-5 w-5" />
                Multi-Container Terminal Environment
              </CardTitle>
              <CardDescription>
                Manage multiple Docker container terminals in tabs, splits, or grid layout
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <MultiTerminal
                height="600px"
                maxTabs={20}
                allowSplit
                allowReorder
                showTabBar
                showToolbar
                defaultTabs={[
                  {
                    type: 'docker',
                    title: 'Docker Terminal',
                    config: selectedContainerId ? { 
                      containerId: selectedContainerId,
                      autoConnect: true,
                      defaultShell: selectedShell
                    } : undefined,
                  },
                ]}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Container Logs */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Container Logs</CardTitle>
                <CardDescription>Real-time container log output</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="border-b p-2">
                  <Select value={selectedContainerId || ''} onValueChange={setSelectedContainerId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select container..." />
                    </SelectTrigger>
                    <SelectContent>
                      {runningContainers.map((container) => (
                        <SelectItem key={container.id} value={container.id}>
                          {container.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <DockerTerminal
                  containerId={selectedContainerId || undefined}
                  height="300px"
                  showToolbar={false}
                  showStatusBar={false}
                />
              </CardContent>
            </Card>

            {/* Resource Usage */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Resource Usage</CardTitle>
                <CardDescription>Real-time container resource monitoring</CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="space-y-4">
                  {runningContainers.slice(0, 3).map((container, index) => (
                    <div key={container.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{container.name}</span>
                        <Badge variant="outline">{container.status}</Badge>
                      </div>
                      
                      {container.cpu !== undefined && (
                        <div>
                          <div className="flex justify-between text-xs mb-1">
                            <span>CPU</span>
                            <span>{container.cpu}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                              style={{ width: `${container.cpu}%` }} 
                            />
                          </div>
                        </div>
                      )}

                      {container.memory !== undefined && (
                        <div>
                          <div className="flex justify-between text-xs mb-1">
                            <span>Memory</span>
                            <span>{container.memory}MB</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-green-600 h-2 rounded-full transition-all duration-300" 
                              style={{ width: `${Math.min((container.memory / 1024) * 100, 100)}%` }} 
                            />
                          </div>
                        </div>
                      )}

                      {index < runningContainers.slice(0, 3).length - 1 && (
                        <Separator className="my-3" />
                      )}
                    </div>
                  ))}
                  
                  {runningContainers.length === 0 && (
                    <div className="text-center py-4 text-muted-foreground">
                      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No running containers to monitor</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Quick Commands */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Quick Commands</CardTitle>
                <CardDescription>Common Docker commands terminal</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <MultiTerminal
                  height="300px"
                  maxTabs={1}
                  showTabBar={false}
                  showToolbar={false}
                  defaultTabs={[
                    { type: 'local', title: 'Docker Commands' },
                  ]}
                />
              </CardContent>
            </Card>

            {/* System Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Docker System Info</CardTitle>
                <CardDescription>Docker daemon and system information</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Status</span>
                    <Badge variant={connected ? 'default' : 'destructive'}>
                      {connected ? 'Connected' : 'Disconnected'}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Containers</span>
                    <span className="text-sm">{containers.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Images</span>
                    <span className="text-sm">{images.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Running</span>
                    <span className="text-sm text-green-600">{runningContainers.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Stopped</span>
                    <span className="text-sm text-red-600">{stoppedContainers.length}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}