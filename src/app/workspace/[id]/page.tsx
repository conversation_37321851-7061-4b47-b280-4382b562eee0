'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { Folder, Bot } from 'lucide-react';

// Workspace components
import { WorkspaceHeader } from '@/components/workspace/interface/WorkspaceHeader';
import { WorkspaceSidebar } from '@/components/workspace/interface/WorkspaceSidebar';
import { WorkspaceEditor } from '@/components/workspace/interface/WorkspaceEditor';
import { WorkspaceTerminal } from '@/components/workspace/interface/WorkspaceTerminal';
import { AIAssistantPanel } from '@/components/workspace/interface/AIAssistantPanel';
import { ResizablePanel } from '@/components/workspace/interface/ResizablePanel';

// Import workspace types and hooks
import { Workspace, WorkspaceStatus } from '@/types/workspace';
import { useWorkspaceInterface } from '@/hooks/useWorkspaceInterface';

interface WorkspaceLayoutState {
  sidebarWidth: number;
  assistantWidth: number;
  terminalHeight: number;
  sidebarCollapsed: boolean;
  assistantCollapsed: boolean;
  terminalCollapsed: boolean;
}

const defaultLayoutState: WorkspaceLayoutState = {
  sidebarWidth: 280,
  assistantWidth: 350,
  terminalHeight: 300,
  sidebarCollapsed: false,
  assistantCollapsed: false,
  terminalCollapsed: false,
};

export default function WorkspacePage() {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const workspaceId = params.id as string;

  // Use workspace interface hook
  const {
    workspace,
    files,
    isLoading,
    error,
    fileOperations,
    workspaceOperations,
    refresh,
  } = useWorkspaceInterface({ workspaceId });

  // Local state for UI
  const [layout, setLayout] = useState<WorkspaceLayoutState>(defaultLayoutState);
  const [activeFile, setActiveFile] = useState<string | null>(null);
  const [openFiles, setOpenFiles] = useState<string[]>([]);

  // Handle layout changes
  const updateLayout = (updates: Partial<WorkspaceLayoutState>) => {
    setLayout(prev => ({ ...prev, ...updates }));
  };

  // Handle layout changes
  const updateLayout = (updates: Partial<WorkspaceLayoutState>) => {
    setLayout(prev => ({ ...prev, ...updates }));
  };

  // Handle file operations
  const handleFileOpen = async (filePath: string) => {
    if (!openFiles.includes(filePath)) {
      setOpenFiles(prev => [...prev, filePath]);
    }
    setActiveFile(filePath);

    // Load file content if it's a file (not directory)
    const file = await fileOperations.openFile(filePath);
    if (file) {
      // File content is now loaded and can be used by the editor
      console.log('File loaded:', file);
    }
  };

  const handleFileClose = (filePath: string) => {
    setOpenFiles(prev => prev.filter(f => f !== filePath));
    if (activeFile === filePath) {
      const remainingFiles = openFiles.filter(f => f !== filePath);
      setActiveFile(remainingFiles.length > 0 ? remainingFiles[remainingFiles.length - 1] : null);
    }
  };

  // Handle workspace operations
  const handleWorkspaceAction = async (action: 'start' | 'stop' | 'restart') => {
    switch (action) {
      case 'start':
        return await workspaceOperations.startWorkspace();
      case 'stop':
        return await workspaceOperations.stopWorkspace();
      case 'restart':
        return await workspaceOperations.restartWorkspace();
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4" />
          <p className="text-muted-foreground">Loading workspace...</p>
        </motion.div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center max-w-md"
        >
          <div className="text-destructive text-4xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold mb-2">Workspace Error</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <button
            onClick={() => router.push('/dashboard/workspaces')}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Back to Workspaces
          </button>
        </motion.div>
      </div>
    );
  }

  // No workspace found
  if (!workspace) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <h2 className="text-xl font-semibold mb-2">Workspace Not Found</h2>
          <p className="text-muted-foreground mb-4">The requested workspace could not be found.</p>
          <button
            onClick={() => router.push('/dashboard/workspaces')}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Back to Workspaces
          </button>
        </motion.div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div
        className="h-screen flex flex-col bg-background overflow-hidden"
        role="application"
        aria-label={`Workspace: ${workspace?.name || 'Loading...'}`}
      >
        {/* Header */}
        <header role="banner">
          <WorkspaceHeader
            workspace={workspace}
            onSettingsClick={() => {}}
            onShareClick={() => {}}
            onStatusClick={() => {}}
            onWorkspaceAction={handleWorkspaceAction}
          />
        </header>

        {/* Main Content */}
        <main
          className="flex-1 flex overflow-hidden"
          role="main"
          aria-label="Workspace content"
        >
          {/* Left Sidebar */}
          <aside
            className={cn(
              "transition-all duration-200",
              layout.sidebarCollapsed && "md:block hidden"
            )}
            role="complementary"
            aria-label="File explorer and tasks"
          >
            <ResizablePanel
              defaultWidth={layout.sidebarWidth}
              minWidth={200}
              maxWidth={500}
              collapsed={layout.sidebarCollapsed}
              onResize={(width) => updateLayout({ sidebarWidth: width })}
              onCollapse={(collapsed) => updateLayout({ sidebarCollapsed: collapsed })}
              side="left"
            >
              <WorkspaceSidebar
                workspaceId={workspaceId}
                onFileSelect={handleFileOpen}
                activeFile={activeFile}
              />
            </ResizablePanel>
          </aside>

          {/* Center Area */}
          <section
            className="flex-1 flex flex-col overflow-hidden min-w-0"
            role="region"
            aria-label="Code editor and terminal"
          >
            {/* Editor */}
            <div
              className="flex-1 overflow-hidden"
              role="region"
              aria-label="Code editor"
            >
              <WorkspaceEditor
                workspaceId={workspaceId}
                activeFile={activeFile}
                openFiles={openFiles}
                onFileClose={handleFileClose}
                onFileOpen={handleFileOpen}
              />
            </div>

            {/* Terminal */}
            <div
              className={cn(
                "transition-all duration-200",
                layout.terminalCollapsed && "sm:block hidden"
              )}
              role="region"
              aria-label="Terminal"
            >
              <ResizablePanel
                defaultHeight={layout.terminalHeight}
                minHeight={150}
                maxHeight={600}
                collapsed={layout.terminalCollapsed}
                onResize={(height) => updateLayout({ terminalHeight: height })}
                onCollapse={(collapsed) => updateLayout({ terminalCollapsed: collapsed })}
                side="bottom"
              >
                <WorkspaceTerminal
                  workspaceId={workspaceId}
                />
              </ResizablePanel>
            </div>
          </section>

          {/* Right AI Assistant Panel */}
          <aside
            className={cn(
              "transition-all duration-200",
              layout.assistantCollapsed && "lg:block hidden"
            )}
            role="complementary"
            aria-label="AI Assistant"
          >
            <ResizablePanel
              defaultWidth={layout.assistantWidth}
              minWidth={300}
              maxWidth={600}
              collapsed={layout.assistantCollapsed}
              onResize={(width) => updateLayout({ assistantWidth: width })}
              onCollapse={(collapsed) => updateLayout({ assistantCollapsed: collapsed })}
              side="right"
            >
              <AIAssistantPanel
                workspaceId={workspaceId}
                workspace={workspace}
              />
            </ResizablePanel>
          </aside>
        </main>

        {/* Mobile Navigation Overlay */}
        {(layout.sidebarCollapsed || layout.assistantCollapsed) && (
          <div className="md:hidden fixed bottom-4 right-4 z-50">
            <div className="flex gap-2">
              {layout.sidebarCollapsed && (
                <Button
                  variant="outline"
                  size="sm"
                  className="h-10 w-10 p-0 bg-background/80 backdrop-blur-sm"
                  onClick={() => updateLayout({ sidebarCollapsed: false })}
                  aria-label="Show file explorer"
                >
                  <Folder className="h-4 w-4" />
                </Button>
              )}
              {layout.assistantCollapsed && (
                <Button
                  variant="outline"
                  size="sm"
                  className="h-10 w-10 p-0 bg-background/80 backdrop-blur-sm"
                  onClick={() => updateLayout({ assistantCollapsed: false })}
                  aria-label="Show AI assistant"
                >
                  <Bot className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}
