"use client"

import * as React from "react"
import { usePathname, useRouter } from "next/navigation"
import {
  Cloud,
  Monitor,
  Plus,
  Settings,
  Activity,
  HardDrive,
  Cpu,
  BarChart3,
  LayoutDashboard,
  Users,
  User,
  Shield,
  CreditCard,
  ChevronRight,
  Search,
  Play,
  Square,
  RotateCcw,
  Trash2,
  ExternalLink,
  Terminal
} from "lucide-react"

import { NavUser } from "@/components/nav-user"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"

// Omnispace navigation data
const data = {
  user: {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/john-doe.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard,
      items: [],
    },
    {
      title: "Workspaces",
      url: "/dashboard/workspaces",
      icon: Monitor,
      items: [
        {
          title: "All Workspaces",
          url: "/dashboard/workspaces",
        },
        {
          title: "Create Workspace",
          url: "/dashboard/workspaces/create",
        },
        {
          title: "Templates",
          url: "/dashboard/workspaces/templates",
        },
      ],
    },
    {
      title: "Monitoring",
      url: "/dashboard/monitoring",
      icon: BarChart3,
      items: [
        {
          title: "System Overview",
          url: "/dashboard/monitoring",
        },
        {
          title: "Performance",
          url: "/dashboard/monitoring/performance",
        },
        {
          title: "Logs",
          url: "/dashboard/monitoring/logs",
        },
        {
          title: "Alerts",
          url: "/dashboard/monitoring/alerts",
        },
      ],
    },
    {
      title: "Terminals",
      url: "/dashboard/terminals",
      icon: Terminal,
      items: [
        {
          title: "Terminal Dashboard",
          url: "/dashboard/terminals",
        },
        {
          title: "SSH Terminal",
          url: "/dashboard/ssh-terminal",
        },
        {
          title: "Docker Terminal",
          url: "/dashboard/docker-terminal",
        },
        {
          title: "SSH Docker",
          url: "/dashboard/ssh-docker",
        },
        {
          title: "Terminal Workspace",
          url: "/dashboard/terminal-workspace",
        },
      ],
    },
    {
      title: "Containers",
      url: "/dashboard/containers",
      icon: HardDrive,
      items: [
        {
          title: "All Containers",
          url: "/dashboard/containers",
        },
        {
          title: "Running",
          url: "/dashboard/containers?status=running",
        },
        {
          title: "Stopped",
          url: "/dashboard/containers?status=stopped",
        },
      ],
    },
    {
      title: "Images",
      url: "/dashboard/images",
      icon: Cloud,
      items: [
        {
          title: "All Images",
          url: "/dashboard/images",
        },
        {
          title: "Build Image",
          url: "/dashboard/images/build",
        },
        {
          title: "Registry",
          url: "/dashboard/images/registry",
        },
      ],
    },
    {
      title: "Settings",
      url: "/dashboard/settings",
      icon: Settings,
      items: [
        {
          title: "Profile",
          url: "/dashboard/settings/profile",
          icon: User,
        },
        {
          title: "Security",
          url: "/dashboard/settings/security",
          icon: Shield,
        },
        {
          title: "Billing",
          url: "/dashboard/settings/billing",
          icon: CreditCard,
        },
        {
          title: "Team",
          url: "/dashboard/settings/team",
          icon: Users,
        },
      ],
    },
  ],
  workspaces: [
    {
      id: "ws-1",
      name: "Ubuntu Desktop",
      status: "running",
      type: "ubuntu-desktop",
      cpu: 2,
      memory: 2048,
      port: 5901,
      lastAccessed: "2 mins ago",
      description: "Primary development environment with GNOME desktop",
      url: "/dashboard/workspaces/ws-1",
    },
    {
      id: "ws-2",
      name: "Development VM",
      status: "stopped",
      type: "ubuntu-server",
      cpu: 4,
      memory: 4096,
      port: 5902,
      lastAccessed: "1 hour ago",
      description: "High-performance VM for heavy development tasks",
      url: "/dashboard/workspaces/ws-2",
    },
    {
      id: "ws-3",
      name: "Test Environment",
      status: "running",
      type: "alpine",
      cpu: 1,
      memory: 1024,
      port: 5903,
      lastAccessed: "30 mins ago",
      description: "Lightweight testing environment",
      url: "/dashboard/workspaces/ws-3",
    },
    {
      id: "ws-4",
      name: "Windows VM",
      status: "starting",
      type: "windows-11",
      cpu: 4,
      memory: 8192,
      port: 5904,
      lastAccessed: "Never",
      description: "Windows 11 virtual machine for cross-platform testing",
      url: "/dashboard/workspaces/ws-4",
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname()
  const router = useRouter()
  const [activeItem, setActiveItem] = React.useState(data.navMain[0])
  const [workspaces, setWorkspaces] = React.useState(data.workspaces)
  const [searchQuery, setSearchQuery] = React.useState("")
  const { setOpen } = useSidebar()

  // Filter workspaces based on search query
  const filteredWorkspaces = workspaces.filter(workspace =>
    workspace.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    workspace.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Handle navigation
  const handleNavigation = (item: typeof data.navMain[0]) => {
    setActiveItem(item)
    router.push(item.url)
    setOpen(true)
  }

  // Handle workspace actions
  const handleWorkspaceAction = (workspaceId: string, action: string) => {
    console.log(`${action} workspace ${workspaceId}`)
    // In a real app, this would call the appropriate API
  }

  // Get status styling
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';
      case 'stopped': return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';
      case 'starting': return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';
      case 'stopping': return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';
    }
  };

  // Get workspace type icon
  const getWorkspaceIcon = (type: string) => {
    switch (type) {
      case 'ubuntu-desktop': return Monitor;
      case 'ubuntu-server': return HardDrive;
      case 'windows-11': return Monitor;
      case 'alpine': return Cloud;
      default: return Monitor;
    }
  };

  return (
    <Sidebar
      collapsible="icon"
      className="overflow-hidden [&>[data-sidebar=sidebar]]:flex-row"
      {...props}
    >
      {/* Main Navigation Sidebar */}
      <Sidebar
        collapsible="none"
        className="!w-[calc(var(--sidebar-width-icon)_+_1px)] border-r"
      >
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild className="md:h-8 md:p-0">
                <a href="/dashboard">
                  <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                    <Cloud className="size-4" />
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">Omnispace</span>
                    <span className="truncate text-xs">Cloud Platform</span>
                  </div>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupContent className="px-1.5 md:px-0">
              <SidebarMenu>
                {data.navMain.map((item) => {
                  const isActive = pathname === item.url || pathname.startsWith(item.url + '/');

                  return (
                    <Collapsible
                      key={item.title}
                      asChild
                      defaultOpen={isActive}
                      className="group/collapsible"
                    >
                      <SidebarMenuItem>
                        <CollapsibleTrigger asChild>
                          <SidebarMenuButton
                            tooltip={{
                              children: item.title,
                              hidden: false,
                            }}
                            onClick={() => handleNavigation(item)}
                            isActive={isActive}
                            className="px-2.5 md:px-2"
                          >
                            <item.icon />
                            <span>{item.title}</span>
                            {item.items?.length > 0 && (
                              <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                            )}
                          </SidebarMenuButton>
                        </CollapsibleTrigger>
                        {item.items?.length > 0 && (
                          <CollapsibleContent>
                            <SidebarMenuSub>
                              {item.items.map((subItem) => (
                                <SidebarMenuSubItem key={subItem.title}>
                                  <SidebarMenuSubButton asChild>
                                    <a href={subItem.url}>
                                      {'icon' in subItem && subItem.icon && <subItem.icon />}
                                      <span>{subItem.title}</span>
                                    </a>
                                  </SidebarMenuSubButton>
                                </SidebarMenuSubItem>
                              ))}
                            </SidebarMenuSub>
                          </CollapsibleContent>
                        )}
                      </SidebarMenuItem>
                    </Collapsible>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
          <NavUser user={data.user} />
        </SidebarFooter>
      </Sidebar>

      {/* Secondary Content Sidebar */}
      <Sidebar collapsible="none" className="hidden flex-1 md:flex">
        <SidebarHeader className="gap-3.5 border-b p-4">
          <div className="flex w-full items-center justify-between">
            <div className="text-foreground text-base font-medium">
              {activeItem?.title === "Workspaces" ? "My Workspaces" : activeItem?.title}
            </div>
            {activeItem?.title === "Workspaces" && (
              <Button size="sm" onClick={() => router.push('/dashboard/workspaces/create')}>
                <Plus className="h-4 w-4 mr-2" />
                New
              </Button>
            )}
          </div>
          <div className="relative">
            <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <SidebarInput
              placeholder={`Search ${activeItem?.title?.toLowerCase()}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup className="px-0">
            <SidebarGroupContent>
              {activeItem?.title === "Workspaces" ? (
                // Workspaces view
                <>
                  {filteredWorkspaces.length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">
                      <Monitor className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No workspaces found</p>
                      <Button
                        size="sm"
                        className="mt-2"
                        onClick={() => router.push('/dashboard/workspaces/create')}
                      >
                        Create Workspace
                      </Button>
                    </div>
                  ) : (
                    filteredWorkspaces.map((workspace) => {
                      const WorkspaceIcon = getWorkspaceIcon(workspace.type);

                      return (
                        <div
                          key={workspace.id}
                          className="hover:bg-sidebar-accent hover:text-sidebar-accent-foreground flex flex-col items-start gap-3 border-b p-4 text-sm leading-tight last:border-b-0 group"
                        >
                          <div className="flex w-full items-center gap-2">
                            <WorkspaceIcon className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium flex-1">{workspace.name}</span>
                            <Badge className={`text-xs px-2 py-1 ${getStatusColor(workspace.status)}`}>
                              {workspace.status}
                            </Badge>
                          </div>

                          <div className="flex w-full items-center gap-4 text-xs text-muted-foreground">
                            <span>{workspace.cpu} CPU</span>
                            <span>{workspace.memory}MB RAM</span>
                            <span>Port {workspace.port}</span>
                          </div>

                          <p className="text-xs text-muted-foreground line-clamp-2 w-full">
                            {workspace.description}
                          </p>

                          <div className="flex w-full items-center justify-between">
                            <span className="text-xs text-muted-foreground">
                              Last accessed: {workspace.lastAccessed}
                            </span>
                            <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              {workspace.status === 'running' ? (
                                <>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    className="h-6 w-6 p-0"
                                    onClick={() => window.open(workspace.url, '_blank')}
                                  >
                                    <ExternalLink className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    className="h-6 w-6 p-0"
                                    onClick={() => handleWorkspaceAction(workspace.id, 'stop')}
                                  >
                                    <Square className="h-3 w-3" />
                                  </Button>
                                </>
                              ) : workspace.status === 'stopped' ? (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                  onClick={() => handleWorkspaceAction(workspace.id, 'start')}
                                >
                                  <Play className="h-3 w-3" />
                                </Button>
                              ) : null}
                              <Button
                                size="sm"
                                variant="ghost"
                                className="h-6 w-6 p-0"
                                onClick={() => handleWorkspaceAction(workspace.id, 'restart')}
                              >
                                <RotateCcw className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      );
                    })
                  )}
                </>
              ) : (
                // Default view for other sections
                <div className="p-4 text-center text-muted-foreground">
                  <activeItem.icon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">Navigate to {activeItem?.title}</p>
                  <Button
                    size="sm"
                    variant="outline"
                    className="mt-2"
                    onClick={() => router.push(activeItem?.url || '/dashboard')}
                  >
                    Go to {activeItem?.title}
                  </Button>
                </div>
              )}
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>
    </Sidebar>
  )
}
