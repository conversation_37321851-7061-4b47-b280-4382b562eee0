'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  Terminal,
  Play,
  Square,
  RotateCcw,
  Trash2,
  Copy,
  Download,
  Settings,
  Plus,
  X,
  ChevronRight,
  Circle,
  CheckCircle,
  AlertCircle,
  Clock,
  Maximize2,
  Minimize2,
  MoreHorizontal,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface WorkspaceTerminalProps {
  workspaceId: string;
  className?: string;
}

interface TerminalSession {
  id: string;
  name: string;
  status: 'active' | 'idle' | 'running' | 'error';
  output: TerminalOutput[];
  currentDirectory: string;
}

interface TerminalOutput {
  id: string;
  type: 'command' | 'output' | 'error' | 'system' | 'progress';
  content: string;
  timestamp: Date;
  metadata?: {
    command?: string;
    exitCode?: number;
    progress?: number;
    duration?: number;
  };
}

const mockTerminalSessions: TerminalSession[] = [
  {
    id: 'main',
    name: 'Main Terminal',
    status: 'running',
    currentDirectory: '/home/<USER>/video-conferencing-system',
    output: [
      {
        id: '1',
        type: 'system',
        content: 'Terminal session started',
        timestamp: new Date(Date.now() - 600000),
      },
      {
        id: '2',
        type: 'command',
        content: 'cd video-conferencing-system && npm install next-auth@beta @auth/prisma-adapter prisma @prisma/client',
        timestamp: new Date(Date.now() - 300000),
        metadata: { command: 'npm install' },
      },
      {
        id: '3',
        type: 'output',
        content: `Need to install the following packages:
  shadcn@2.10.0
Ok to proceed? (y)`,
        timestamp: new Date(Date.now() - 280000),
      },
      {
        id: '4',
        type: 'progress',
        content: 'Installing packages...',
        timestamp: new Date(Date.now() - 260000),
        metadata: { progress: 75 },
      },
      {
        id: '5',
        type: 'output',
        content: `✓ Installing dependencies
✓ Initializing project
✓ Installing components
✓ Updating tailwind.config.js
✓ Updating globals.css
✓ Installing lucide-react
✓ Updated import paths
✓ Updated tsconfig.json

Success! Project has been initialized.`,
        timestamp: new Date(Date.now() - 120000),
      },
      {
        id: '6',
        type: 'command',
        content: 'npx shadcn@latest init -y',
        timestamp: new Date(Date.now() - 60000),
        metadata: { command: 'npx shadcn@latest init -y', exitCode: 0 },
      },
      {
        id: '7',
        type: 'output',
        content: `✓ Writing components.json...
✓ Initializing project...
✓ Installing dependencies...

Success! Project initialized.`,
        timestamp: new Date(Date.now() - 30000),
      },
    ],
  },
  {
    id: 'dev',
    name: 'Dev Server',
    status: 'idle',
    currentDirectory: '/home/<USER>/video-conferencing-system',
    output: [
      {
        id: '1',
        type: 'system',
        content: 'Development server terminal',
        timestamp: new Date(Date.now() - 120000),
      },
    ],
  },
];

export function WorkspaceTerminal({ workspaceId, className }: WorkspaceTerminalProps) {
  const [sessions, setSessions] = useState<TerminalSession[]>(mockTerminalSessions);
  const [activeSessionId, setActiveSessionId] = useState('main');
  const [commandInput, setCommandInput] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const outputEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const activeSession = sessions.find(s => s.id === activeSessionId);

  const scrollToBottom = () => {
    outputEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [activeSession?.output]);

  const handleCommandSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!commandInput.trim() || !activeSession) return;

    const newOutput: TerminalOutput = {
      id: Date.now().toString(),
      type: 'command',
      content: commandInput,
      timestamp: new Date(),
      metadata: { command: commandInput },
    };

    setSessions(prev => prev.map(session => 
      session.id === activeSessionId
        ? { ...session, output: [...session.output, newOutput], status: 'running' as const }
        : session
    ));

    setCommandInput('');

    // Simulate command execution
    setTimeout(() => {
      const responseOutput: TerminalOutput = {
        id: (Date.now() + 1).toString(),
        type: 'output',
        content: `Command executed: ${commandInput}`,
        timestamp: new Date(),
      };

      setSessions(prev => prev.map(session => 
        session.id === activeSessionId
          ? { ...session, output: [...session.output, responseOutput], status: 'idle' as const }
          : session
      ));
    }, 1000);
  };

  const createNewSession = () => {
    const newSession: TerminalSession = {
      id: `session-${Date.now()}`,
      name: `Terminal ${sessions.length + 1}`,
      status: 'idle',
      currentDirectory: '/home/<USER>/video-conferencing-system',
      output: [
        {
          id: '1',
          type: 'system',
          content: 'New terminal session started',
          timestamp: new Date(),
        },
      ],
    };

    setSessions(prev => [...prev, newSession]);
    setActiveSessionId(newSession.id);
  };

  const closeSession = (sessionId: string) => {
    if (sessions.length <= 1) return;
    
    setSessions(prev => prev.filter(s => s.id !== sessionId));
    if (activeSessionId === sessionId) {
      setActiveSessionId(sessions.find(s => s.id !== sessionId)?.id || sessions[0].id);
    }
  };

  const clearSession = () => {
    if (!activeSession) return;
    
    setSessions(prev => prev.map(session => 
      session.id === activeSessionId
        ? { ...session, output: [] }
        : session
    ));
  };

  const getStatusIcon = (status: TerminalSession['status']) => {
    switch (status) {
      case 'running':
        return <Circle className="h-2 w-2 fill-green-500 text-green-500 animate-pulse" />;
      case 'error':
        return <Circle className="h-2 w-2 fill-red-500 text-red-500" />;
      case 'active':
        return <Circle className="h-2 w-2 fill-blue-500 text-blue-500" />;
      default:
        return <Circle className="h-2 w-2 fill-gray-500 text-gray-500" />;
    }
  };

  const getOutputIcon = (type: TerminalOutput['type']) => {
    switch (type) {
      case 'command':
        return <ChevronRight className="h-3 w-3 text-blue-500" />;
      case 'error':
        return <AlertCircle className="h-3 w-3 text-red-500" />;
      case 'system':
        return <Circle className="h-3 w-3 text-yellow-500" />;
      case 'progress':
        return <Clock className="h-3 w-3 text-blue-500" />;
      default:
        return null;
    }
  };

  const renderOutput = (output: TerminalOutput) => {
    const Icon = getOutputIcon(output.type);

    return (
      <motion.div
        key={output.id}
        initial={{ opacity: 0, y: 5 }}
        animate={{ opacity: 1, y: 0 }}
        className={cn(
          "flex gap-2 py-1 px-2 text-sm font-mono",
          output.type === 'command' && "text-blue-400",
          output.type === 'error' && "text-red-400",
          output.type === 'system' && "text-yellow-400 italic",
          output.type === 'output' && "text-green-300"
        )}
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          {Icon && <div className="flex-shrink-0">{Icon}</div>}
          <span className="text-xs text-muted-foreground flex-shrink-0">
            {output.timestamp.toLocaleTimeString([], { 
              hour: '2-digit', 
              minute: '2-digit',
              second: '2-digit'
            })}
          </span>
          <pre className="whitespace-pre-wrap break-words flex-1 min-w-0">
            {output.content}
          </pre>
        </div>
        {output.metadata?.progress !== undefined && (
          <div className="flex items-center gap-2 flex-shrink-0">
            <Progress value={output.metadata.progress} className="w-20 h-2" />
            <span className="text-xs text-muted-foreground">
              {output.metadata.progress}%
            </span>
          </div>
        )}
      </motion.div>
    );
  };

  return (
    <div
      className={cn("h-full flex flex-col bg-black text-green-400 border-t border-border", className)}
      role="region"
      aria-label="Terminal"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-2 bg-muted/10 border-b border-border">
        <div className="flex items-center gap-2">
          <Terminal className="h-4 w-4" />
          <span className="text-sm font-medium" id="terminal-title">Terminal</span>
          {activeSession && (
            <Badge
              variant="secondary"
              className="text-xs"
              aria-label={`Current directory: ${activeSession.currentDirectory.split('/').pop()}`}
            >
              {activeSession.currentDirectory.split('/').pop()}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
            onClick={createNewSession}
          >
            <Plus className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
            onClick={clearSession}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            {isFullscreen ? (
              <Minimize2 className="h-3 w-3" />
            ) : (
              <Maximize2 className="h-3 w-3" />
            )}
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Copy className="h-4 w-4 mr-2" />
                Copy Output
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Export Log
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Session Tabs */}
      <div className="flex items-center border-b border-border bg-muted/5">
        <ScrollArea className="flex-1">
          <div className="flex">
            {sessions.map((session) => (
              <div
                key={session.id}
                className={cn(
                  "flex items-center gap-2 px-3 py-1 border-r border-border cursor-pointer hover:bg-muted/20 transition-colors",
                  activeSessionId === session.id && "bg-muted/30 border-b-2 border-b-primary"
                )}
                onClick={() => setActiveSessionId(session.id)}
              >
                {getStatusIcon(session.status)}
                <span className="text-xs">{session.name}</span>
                {sessions.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-3 w-3 p-0 opacity-0 group-hover:opacity-100 hover:bg-destructive/20"
                    onClick={(e) => {
                      e.stopPropagation();
                      closeSession(session.id);
                    }}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Terminal Output */}
      <ScrollArea
        className="flex-1 bg-black"
        aria-label="Terminal output"
        role="log"
        aria-live="polite"
        aria-describedby="terminal-title"
      >
        <div className="p-2">
          {activeSession?.output.map(renderOutput)}
          <div ref={outputEndRef} />
        </div>
      </ScrollArea>

      {/* Command Input */}
      <form
        onSubmit={handleCommandSubmit}
        className="flex items-center gap-2 p-2 border-t border-border bg-muted/5"
        role="form"
        aria-label="Terminal command input"
      >
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span className="text-blue-400">$</span>
          <span className="text-green-400">{activeSession?.currentDirectory}</span>
          <ChevronRight className="h-3 w-3" />
        </div>
        <Input
          ref={inputRef}
          value={commandInput}
          onChange={(e) => setCommandInput(e.target.value)}
          placeholder="Enter command..."
          className="flex-1 bg-transparent border-none text-green-400 font-mono text-sm focus:ring-0 focus:border-none"
          autoComplete="off"
          aria-label="Terminal command"
          aria-describedby="terminal-help"
        />
        <Button
          type="submit"
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
          disabled={!commandInput.trim()}
          aria-label="Execute command"
        >
          <Play className="h-3 w-3" />
        </Button>

        {/* Screen reader help text */}
        <div id="terminal-help" className="sr-only">
          Enter commands to execute in the terminal. Press Enter to run.
        </div>
      </form>
    </div>
  );
}
