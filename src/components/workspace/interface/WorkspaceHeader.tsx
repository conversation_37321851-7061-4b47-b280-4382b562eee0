'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Settings,
  Share,
  Star,
  StarOff,
  Play,
  Square,
  RotateCcw,
  MoreHorizontal,
  Users,
  Clock,
  Activity,
  Circle,
  CheckCircle,
  AlertCircle,
  Loader2,
  ExternalLink,
  Copy,
  Download,
  Trash2,
  Edit,
  Bookmark,
  BookmarkCheck,
  Zap,
  Globe,
  Lock,
  Eye,
  EyeOff,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface WorkspaceHeaderProps {
  workspace: {
    id: string;
    name: string;
    description?: string;
    status: 'running' | 'stopped' | 'starting' | 'stopping' | 'error' | 'active' | 'creating' | 'archived';
    type: string;
    createdAt: string;
    updatedAt: string;
    userId?: string;
    ownerId?: string;
  };
  onSettingsClick: () => void;
  onShareClick: () => void;
  onStatusClick: () => void;
  onWorkspaceAction?: (action: 'start' | 'stop' | 'restart') => Promise<boolean>;
  className?: string;
}

interface WorkspaceStats {
  uptime: string;
  activeUsers: number;
  cpuUsage: number;
  memoryUsage: number;
  lastActivity: string;
}

const mockStats: WorkspaceStats = {
  uptime: '2h 34m',
  activeUsers: 1,
  cpuUsage: 45,
  memoryUsage: 68,
  lastActivity: '2 minutes ago',
};

const mockCollaborators = [
  {
    id: '1',
    name: 'Manus',
    email: '<EMAIL>',
    avatar: null,
    status: 'online',
    role: 'owner',
  },
  {
    id: '2',
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: null,
    status: 'away',
    role: 'editor',
  },
];

export function WorkspaceHeader({
  workspace,
  onSettingsClick,
  onShareClick,
  onStatusClick,
  onWorkspaceAction,
  className
}: WorkspaceHeaderProps) {
  const router = useRouter();
  const [isFavorite, setIsFavorite] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);

  const getStatusIcon = () => {
    switch (workspace.status) {
      case 'running':
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'starting':
      case 'creating':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'stopping':
        return <Loader2 className="h-4 w-4 text-orange-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'stopped':
      case 'archived':
        return <Circle className="h-4 w-4 text-gray-500" />;
      default:
        return <Circle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (workspace.status) {
      case 'running':
      case 'active':
        return 'bg-green-500/10 text-green-700 border-green-500/20';
      case 'starting':
      case 'creating':
        return 'bg-blue-500/10 text-blue-700 border-blue-500/20';
      case 'stopping':
        return 'bg-orange-500/10 text-orange-700 border-orange-500/20';
      case 'error':
        return 'bg-red-500/10 text-red-700 border-red-500/20';
      case 'stopped':
      case 'archived':
        return 'bg-gray-500/10 text-gray-700 border-gray-500/20';
      default:
        return 'bg-gray-500/10 text-gray-700 border-gray-500/20';
    }
  };

  const handleWorkspaceAction = async (action: 'start' | 'stop' | 'restart') => {
    if (onWorkspaceAction) {
      const success = await onWorkspaceAction(action);
      if (success) {
        console.log(`${action} workspace successful:`, workspace.id);
      } else {
        console.error(`${action} workspace failed:`, workspace.id);
      }
    } else {
      console.log(`${action} workspace:`, workspace.id);
      onStatusClick();
    }
  };

  const handleCopyWorkspaceUrl = () => {
    const url = `${window.location.origin}/workspace/${workspace.id}`;
    navigator.clipboard.writeText(url);
    // Show toast notification
  };

  return (
    <TooltipProvider>
      <div className={cn(
        "flex items-center justify-between px-4 py-3 bg-background border-b border-border",
        "backdrop-blur-sm bg-background/95",
        className
      )}>
        {/* Left Section */}
        <div className="flex items-center gap-4">
          {/* Back Button */}
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => router.push('/dashboard/workspaces')}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>

          {/* Workspace Info */}
          <div className="flex items-center gap-3">
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <h1 className="text-lg font-semibold truncate max-w-64">
                  {workspace.name}
                </h1>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={() => setIsFavorite(!isFavorite)}
                >
                  {isFavorite ? (
                    <Star className="h-3 w-3 fill-yellow-500 text-yellow-500" />
                  ) : (
                    <StarOff className="h-3 w-3 text-muted-foreground" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={() => setIsBookmarked(!isBookmarked)}
                >
                  {isBookmarked ? (
                    <BookmarkCheck className="h-3 w-3 fill-blue-500 text-blue-500" />
                  ) : (
                    <Bookmark className="h-3 w-3 text-muted-foreground" />
                  )}
                </Button>
              </div>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Badge variant="outline" className="text-xs px-1 py-0">
                  {workspace.type}
                </Badge>
                <span>•</span>
                <span>Created {new Date(workspace.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          {/* Status Badge */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge 
                variant="outline" 
                className={cn("cursor-pointer", getStatusColor())}
                onClick={onStatusClick}
              >
                {getStatusIcon()}
                <span className="ml-1 capitalize">{workspace.status}</span>
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-xs">
                <div>Status: {workspace.status}</div>
                <div>Uptime: {mockStats.uptime}</div>
                <div>CPU: {mockStats.cpuUsage}%</div>
                <div>Memory: {mockStats.memoryUsage}%</div>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Center Section - Collaborators */}
        <div className="flex items-center gap-2">
          <div className="flex -space-x-2">
            {mockCollaborators.map((collaborator) => (
              <Tooltip key={collaborator.id}>
                <TooltipTrigger asChild>
                  <div className="relative">
                    <Avatar className="h-7 w-7 border-2 border-background">
                      <AvatarImage src={collaborator.avatar || undefined} />
                      <AvatarFallback className="text-xs">
                        {collaborator.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className={cn(
                      "absolute -bottom-0.5 -right-0.5 h-2.5 w-2.5 rounded-full border border-background",
                      collaborator.status === 'online' ? 'bg-green-500' : 'bg-gray-400'
                    )} />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-xs">
                    <div className="font-medium">{collaborator.name}</div>
                    <div className="text-muted-foreground">{collaborator.role}</div>
                    <div className="text-muted-foreground capitalize">{collaborator.status}</div>
                  </div>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>
          {mockCollaborators.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 px-2 text-xs"
              onClick={onShareClick}
            >
              <Users className="h-3 w-3 mr-1" />
              {mockCollaborators.length}
            </Button>
          )}
        </div>

        {/* Right Section - Actions */}
        <div className="flex items-center gap-2">
          {/* Workspace Controls */}
          <div className="flex items-center gap-1">
            {(workspace.status === 'running' || workspace.status === 'active') ? (
              <>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={() => handleWorkspaceAction('restart')}
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Restart workspace</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={() => handleWorkspaceAction('stop')}
                    >
                      <Square className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Stop workspace</TooltipContent>
                </Tooltip>
              </>
            ) : (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => handleWorkspaceAction('start')}
                    disabled={workspace.status === 'starting'}
                  >
                    <Play className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Start workspace</TooltipContent>
              </Tooltip>
            )}
          </div>

          {/* Share Button */}
          <Button
            variant="outline"
            size="sm"
            className="h-8 px-3"
            onClick={onShareClick}
          >
            <Share className="h-3 w-3 mr-1" />
            Share
          </Button>

          {/* More Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={handleCopyWorkspaceUrl}>
                <Copy className="h-4 w-4 mr-2" />
                Copy URL
              </DropdownMenuItem>
              <DropdownMenuItem>
                <ExternalLink className="h-4 w-4 mr-2" />
                Open in New Tab
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Export Workspace
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onSettingsClick}>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Edit className="h-4 w-4 mr-2" />
                Rename
              </DropdownMenuItem>
              <DropdownMenuItem className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </TooltipProvider>
  );
}
