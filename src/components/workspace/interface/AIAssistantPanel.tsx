'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  Bo<PERSON>,
  User,
  Send,
  Paperclip,
  Mic,
  <PERSON>c<PERSON><PERSON>,
  Settings,
  Maximize2,
  Minimize2,
  MoreVertical,
  Circle,
  Play,
  Square,
  Terminal,
  Code,
  FileText,
  Zap,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Progress } from '@/components/ui/progress';

interface AIAssistantPanelProps {
  workspaceId: string;
  workspace: any;
  className?: string;
}

interface Message {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  status?: 'sending' | 'sent' | 'error';
  metadata?: {
    command?: string;
    output?: string;
    progress?: number;
  };
}

interface AssistantStatus {
  status: 'idle' | 'thinking' | 'executing' | 'error';
  currentTask?: string;
  progress?: number;
}

const mockMessages: Message[] = [
  {
    id: '1',
    type: 'system',
    content: 'Manus is using Editor',
    timestamp: new Date(Date.now() - 300000),
  },
  {
    id: '2',
    type: 'system',
    content: 'Creating file video-conferencing-system/src/app/api/meetings/[meetingId]/route.ts',
    timestamp: new Date(Date.now() - 240000),
  },
  {
    id: '3',
    type: 'assistant',
    content: 'I confirmed the Next.js project was successfully created with TypeScript, Tailwind, ESLint, and the app directory structure. Next, I will start setting up the project dependencies and initial configuration to build the scalable video conferencing platform.',
    timestamp: new Date(Date.now() - 180000),
  },
  {
    id: '4',
    type: 'system',
    content: 'Executing command: npx shadcn@latest init -y',
    timestamp: new Date(Date.now() - 120000),
    metadata: {
      command: 'npx shadcn@latest init -y',
      progress: 100,
    },
  },
  {
    id: '5',
    type: 'assistant',
    content: 'I successfully generated the Next.js project with TypeScript, Tailwind, ESLint, and app directory. Attempted to install dependencies, but some packages like @radix-ui/react* do not exist in npm registry, causing errors. Next, I will remove or replace invalid packages and proceed with setting up the core dependencies for the video conferencing system.',
    timestamp: new Date(Date.now() - 60000),
  },
];

export function AIAssistantPanel({ workspaceId, workspace, className }: AIAssistantPanelProps) {
  const [messages, setMessages] = useState<Message[]>(mockMessages);
  const [inputValue, setInputValue] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [assistantStatus, setAssistantStatus] = useState<AssistantStatus>({
    status: 'thinking',
    currentTask: 'Set up project structure and dependencies',
    progress: 75,
  });
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
      status: 'sending',
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue('');

    // Simulate assistant response
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: 'sent' }
            : msg
        )
      );

      // Add assistant response
      setTimeout(() => {
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          content: 'I understand your request. Let me help you with that.',
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, assistantMessage]);
      }, 1000);
    }, 500);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getStatusIcon = () => {
    switch (assistantStatus.status) {
      case 'thinking':
        return <Loader2 className="h-3 w-3 animate-spin text-blue-500" />;
      case 'executing':
        return <Play className="h-3 w-3 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-3 w-3 text-red-500" />;
      default:
        return <Circle className="h-3 w-3 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (assistantStatus.status) {
      case 'thinking':
        return 'text-blue-500';
      case 'executing':
        return 'text-green-500';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const renderMessage = (message: Message) => {
    const isUser = message.type === 'user';
    const isSystem = message.type === 'system';

    if (isSystem) {
      return (
        <motion.div
          key={message.id}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 px-3 py-2 text-xs text-muted-foreground bg-muted/30 rounded-md mx-3 my-1"
        >
          {message.content.includes('Executing command') && (
            <Terminal className="h-3 w-3 text-blue-500" />
          )}
          {message.content.includes('Creating file') && (
            <FileText className="h-3 w-3 text-green-500" />
          )}
          {message.content.includes('using') && (
            <Code className="h-3 w-3 text-purple-500" />
          )}
          <span className="flex-1">{message.content}</span>
          {message.metadata?.progress !== undefined && (
            <div className="flex items-center gap-2 ml-2">
              <Progress value={message.metadata.progress} className="w-16 h-1" />
              <span className="text-xs">{message.metadata.progress}%</span>
            </div>
          )}
        </motion.div>
      );
    }

    return (
      <motion.div
        key={message.id}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={cn(
          "flex gap-3 px-3 py-3 mx-3 my-2 rounded-lg",
          isUser 
            ? "bg-primary/10 ml-8" 
            : "bg-muted/50 mr-8"
        )}
      >
        <div className={cn(
          "flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center",
          isUser 
            ? "bg-primary text-primary-foreground" 
            : "bg-muted-foreground text-background"
        )}>
          {isUser ? (
            <User className="h-3 w-3" />
          ) : (
            <Bot className="h-3 w-3" />
          )}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-xs font-medium">
              {isUser ? 'You' : 'Manus'}
            </span>
            <span className="text-xs text-muted-foreground">
              {message.timestamp.toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </span>
            {message.status === 'sending' && (
              <Loader2 className="h-3 w-3 animate-spin text-muted-foreground" />
            )}
          </div>
          <p className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </p>
        </div>
      </motion.div>
    );
  };

  return (
    <div
      className={cn("h-full flex flex-col bg-card border-l border-border", className)}
      role="complementary"
      aria-label="AI Assistant"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <h3 className="font-semibold text-sm" id="assistant-title">Manus's Computer</h3>
          </div>
          <Badge
            variant="secondary"
            className="text-xs"
            aria-label={`Assistant status: ${assistantStatus.status}`}
          >
            {assistantStatus.status}
          </Badge>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-7 p-0"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <Minimize2 className="h-3 w-3" />
            ) : (
              <Maximize2 className="h-3 w-3" />
            )}
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <MoreVertical className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                Clear Chat
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Status Bar */}
      {assistantStatus.currentTask && (
        <div className="px-4 py-2 bg-muted/30 border-b border-border">
          <div className="flex items-center gap-2 text-xs">
            <Zap className="h-3 w-3 text-blue-500" />
            <span className="flex-1 truncate">{assistantStatus.currentTask}</span>
            {assistantStatus.progress !== undefined && (
              <span className="text-muted-foreground">{assistantStatus.progress}%</span>
            )}
          </div>
          {assistantStatus.progress !== undefined && (
            <Progress value={assistantStatus.progress} className="mt-2 h-1" />
          )}
        </div>
      )}

      {/* Messages */}
      <ScrollArea
        className="flex-1"
        aria-label="Chat messages"
        role="log"
        aria-live="polite"
        aria-describedby="assistant-title"
      >
        <div className="py-2">
          {messages.map(renderMessage)}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="p-4 border-t border-border">
        <form onSubmit={(e) => { e.preventDefault(); handleSendMessage(); }}>
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Textarea
                ref={inputRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Send message to Manus..."
                className="min-h-[40px] max-h-[120px] resize-none pr-20"
                rows={1}
                aria-label="Message input"
                aria-describedby="input-help"
              />
            <div className="absolute right-2 bottom-2 flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => setIsRecording(!isRecording)}
              >
                {isRecording ? (
                  <MicOff className="h-3 w-3 text-red-500" />
                ) : (
                  <Mic className="h-3 w-3" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
              >
                <Paperclip className="h-3 w-3" />
              </Button>
            </div>
          </div>
            <Button
              type="submit"
              disabled={!inputValue.trim()}
              size="sm"
              className="h-10 px-3"
              aria-label="Send message"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </form>

        {/* Quick Actions */}
        <div className="flex gap-2 mt-2" role="group" aria-label="Quick actions">
          <Button
            variant="outline"
            size="sm"
            className="h-7 text-xs"
            aria-label="Run terminal command"
          >
            <Terminal className="h-3 w-3 mr-1" />
            Run Command
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="h-7 text-xs"
            aria-label="Generate code"
          >
            <Code className="h-3 w-3 mr-1" />
            Generate Code
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="h-7 text-xs"
            aria-label="Explain code"
          >
            <FileText className="h-3 w-3 mr-1" />
            Explain
          </Button>
        </div>

        {/* Screen reader help text */}
        <div id="input-help" className="sr-only">
          Press Enter to send message, Shift+Enter for new line
        </div>
      </div>
    </div>
  );
}
