'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  X,
  Circle,
  Save,
  Undo,
  Redo,
  Search,
  Replace,
  Settings,
  MoreHorizontal,
  FileText,
  Code,
  Image,
  File,
  Maximize2,
  Minimize2,
  Copy,
  Cut,
  Paste,
  Download,
  Upload,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface WorkspaceEditorProps {
  workspaceId: string;
  activeFile: string | null;
  openFiles: string[];
  onFileClose: (filePath: string) => void;
  onFileOpen: (filePath: string) => void;
  className?: string;
}

interface FileContent {
  path: string;
  content: string;
  language: string;
  modified: boolean;
  saved: boolean;
}

const mockFileContents: Record<string, FileContent> = {
  '/src/app/api/meetings/[meetingId]/route.ts': {
    path: '/src/app/api/meetings/[meetingId]/route.ts',
    language: 'typescript',
    modified: false,
    saved: true,
    content: `import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { z } from "zod";

const updateMeetingSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  description: z.string().optional(),
  scheduledAt: z.string().datetime().optional(),
  maxParticipants: z.number().min(2).max(100).optional(),
  recordingEnabled: z.boolean().optional(),
  status: z.enum(['scheduled', 'active', 'ended', 'cancelled']).optional(),
});

// GET /api/meetings/[meetingId] - Get meeting details
export async function GET(
  request: NextRequest,
  { params }: { params: { meetingId: string } }
) {
  try {
    const session = await auth();
    const { meetingId } = params;

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const meeting = await db.meeting.findUnique({
      where: { id: meetingId },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
        chatMessages: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
          orderBy: { createdAt: 'asc' },
        },
        transcriptions: {
          orderBy: { timestamp: 'asc' },
        },
        sharedFiles: {
          include: {
            uploadedBy: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
      },
    });

    if (!meeting) {
      return NextResponse.json(
        { success: false, error: 'Meeting not found' },
        { status: 404 }
      );
    }

    // Check if user has access to this meeting
    const hasAccess = meeting.ownerId === session.user.id ||
      meeting.participants.some(p => p.userId === session.user.id);

    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      data: meeting,
    });
  } catch (error) {
    console.error('Error fetching meeting:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}`
  },
  '/src/components/Header.tsx': {
    path: '/src/components/Header.tsx',
    language: 'typescript',
    modified: true,
    saved: false,
    content: `import React from 'react';
import { Button } from '@/components/ui/button';

interface HeaderProps {
  title: string;
  onMenuClick?: () => void;
}

export function Header({ title, onMenuClick }: HeaderProps) {
  return (
    <header className="flex items-center justify-between p-4 border-b">
      <h1 className="text-xl font-semibold">{title}</h1>
      <Button variant="outline" onClick={onMenuClick}>
        Menu
      </Button>
    </header>
  );
}`
  },
};

export function WorkspaceEditor({ 
  workspaceId, 
  activeFile, 
  openFiles, 
  onFileClose, 
  onFileOpen,
  className 
}: WorkspaceEditorProps) {
  const [fileContents, setFileContents] = useState<Record<string, FileContent>>(mockFileContents);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const editorRef = useRef<HTMLTextAreaElement>(null);

  const getFileIcon = (filePath: string) => {
    const extension = filePath.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'tsx':
      case 'ts':
      case 'js':
      case 'jsx':
        return Code;
      case 'md':
        return FileText;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return Image;
      default:
        return File;
    }
  };

  const getLanguageBadge = (language: string) => {
    const colors: Record<string, string> = {
      typescript: 'bg-blue-500',
      javascript: 'bg-yellow-500',
      css: 'bg-purple-500',
      html: 'bg-orange-500',
      json: 'bg-green-500',
      markdown: 'bg-gray-500',
    };
    return colors[language] || 'bg-gray-500';
  };

  const handleContentChange = (filePath: string, content: string) => {
    setFileContents(prev => ({
      ...prev,
      [filePath]: {
        ...prev[filePath],
        content,
        modified: true,
        saved: false,
      }
    }));
  };

  const handleSaveFile = (filePath: string) => {
    setFileContents(prev => ({
      ...prev,
      [filePath]: {
        ...prev[filePath],
        modified: false,
        saved: true,
      }
    }));
    // Here you would typically save to the backend
  };

  const currentFile = activeFile ? fileContents[activeFile] : null;

  if (openFiles.length === 0) {
    return (
      <div className={cn("h-full flex items-center justify-center bg-background", className)}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <Code className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Files Open</h3>
          <p className="text-muted-foreground mb-4">
            Select a file from the sidebar to start editing
          </p>
          <Button variant="outline" onClick={() => onFileOpen('/src/App.tsx')}>
            Open a File
          </Button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className={cn("h-full flex flex-col bg-background", className)}>
      {/* Tab Bar */}
      <div className="flex items-center border-b border-border bg-muted/30">
        <ScrollArea className="flex-1">
          <div className="flex">
            {openFiles.map((filePath) => {
              const file = fileContents[filePath];
              const Icon = getFileIcon(filePath);
              const fileName = filePath.split('/').pop() || filePath;
              const isActive = activeFile === filePath;

              return (
                <motion.div
                  key={filePath}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className={cn(
                    "flex items-center gap-2 px-3 py-2 border-r border-border cursor-pointer hover:bg-accent/50 transition-colors min-w-0",
                    isActive && "bg-background border-b-2 border-b-primary"
                  )}
                  onClick={() => onFileOpen(filePath)}
                >
                  <Icon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <span className="text-sm truncate max-w-32">{fileName}</span>
                  {file?.modified && (
                    <Circle className="h-2 w-2 fill-orange-500 text-orange-500 flex-shrink-0" />
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 hover:bg-destructive/20 flex-shrink-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      onFileClose(filePath);
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </motion.div>
              );
            })}
          </div>
        </ScrollArea>

        {/* Editor Actions */}
        <div className="flex items-center gap-1 px-2">
          <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
            <Search className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
            <Replace className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-7 p-0"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            {isFullscreen ? (
              <Minimize2 className="h-3 w-3" />
            ) : (
              <Maximize2 className="h-3 w-3" />
            )}
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Cut className="h-4 w-4 mr-2" />
                Cut
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Paste className="h-4 w-4 mr-2" />
                Paste
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Download
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Upload className="h-4 w-4 mr-2" />
                Upload
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Editor Content */}
      {currentFile && (
        <div className="flex-1 flex flex-col">
          {/* File Info Bar */}
          <div className="flex items-center justify-between px-4 py-2 bg-muted/20 border-b border-border">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Badge 
                  variant="secondary" 
                  className={cn("text-xs text-white", getLanguageBadge(currentFile.language))}
                >
                  {currentFile.language}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {currentFile.path}
                </span>
              </div>
              {currentFile.modified && (
                <Badge variant="outline" className="text-xs">
                  Modified
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-7 px-2"
                onClick={() => handleSaveFile(currentFile.path)}
                disabled={!currentFile.modified}
              >
                <Save className="h-3 w-3 mr-1" />
                Save
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <Undo className="h-3 w-3" />
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <Redo className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Code Editor */}
          <div className="flex-1 relative">
            <textarea
              ref={editorRef}
              value={currentFile.content}
              onChange={(e) => handleContentChange(currentFile.path, e.target.value)}
              className={cn(
                "w-full h-full p-4 bg-background text-foreground font-mono text-sm resize-none border-none outline-none",
                "focus:ring-0 focus:border-none"
              )}
              style={{
                lineHeight: '1.5',
                tabSize: 2,
              }}
              spellCheck={false}
            />
            
            {/* Line Numbers (simplified) */}
            <div className="absolute left-0 top-0 w-12 h-full bg-muted/30 border-r border-border pointer-events-none">
              <div className="p-4 font-mono text-xs text-muted-foreground leading-6">
                {currentFile.content.split('\n').map((_, index) => (
                  <div key={index} className="text-right pr-2">
                    {index + 1}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
