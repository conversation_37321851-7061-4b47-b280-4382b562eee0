'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight, ChevronUp, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ResizablePanelProps {
  children: React.ReactNode;
  defaultWidth?: number;
  defaultHeight?: number;
  minWidth?: number;
  maxWidth?: number;
  minHeight?: number;
  maxHeight?: number;
  collapsed?: boolean;
  onResize?: (size: number) => void;
  onCollapse?: (collapsed: boolean) => void;
  side: 'left' | 'right' | 'top' | 'bottom';
  className?: string;
}

export function ResizablePanel({
  children,
  defaultWidth = 300,
  defaultHeight = 200,
  minWidth = 200,
  maxWidth = 800,
  minHeight = 100,
  maxHeight = 600,
  collapsed = false,
  onResize,
  onCollapse,
  side,
  className,
}: ResizablePanelProps) {
  const [size, setSize] = useState(side === 'left' || side === 'right' ? defaultWidth : defaultHeight);
  const [isResizing, setIsResizing] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(collapsed);
  const panelRef = useRef<HTMLDivElement>(null);
  const startPos = useRef(0);
  const startSize = useRef(0);

  const isHorizontal = side === 'left' || side === 'right';
  const minSize = isHorizontal ? minWidth : minHeight;
  const maxSize = isHorizontal ? maxWidth : maxHeight;

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
    startPos.current = isHorizontal ? e.clientX : e.clientY;
    startSize.current = size;
    document.body.style.cursor = isHorizontal ? 'col-resize' : 'row-resize';
    document.body.style.userSelect = 'none';
  }, [isHorizontal, size]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing) return;

    const currentPos = isHorizontal ? e.clientX : e.clientY;
    let delta = currentPos - startPos.current;

    // Adjust delta based on panel side
    if (side === 'right' || side === 'bottom') {
      delta = -delta;
    }

    const newSize = Math.max(minSize, Math.min(maxSize, startSize.current + delta));
    setSize(newSize);
    onResize?.(newSize);
  }, [isResizing, isHorizontal, side, minSize, maxSize, onResize]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, []);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const toggleCollapse = () => {
    const newCollapsed = !isCollapsed;
    setIsCollapsed(newCollapsed);
    onCollapse?.(newCollapsed);
  };

  const getCollapseIcon = () => {
    if (isCollapsed) {
      switch (side) {
        case 'left': return ChevronRight;
        case 'right': return ChevronLeft;
        case 'top': return ChevronDown;
        case 'bottom': return ChevronUp;
      }
    } else {
      switch (side) {
        case 'left': return ChevronLeft;
        case 'right': return ChevronRight;
        case 'top': return ChevronUp;
        case 'bottom': return ChevronDown;
      }
    }
  };

  const CollapseIcon = getCollapseIcon();

  const getResizeHandleClasses = () => {
    const baseClasses = "absolute bg-border hover:bg-primary/50 transition-colors z-10 group";
    
    switch (side) {
      case 'left':
        return cn(baseClasses, "right-0 top-0 w-1 h-full cursor-col-resize");
      case 'right':
        return cn(baseClasses, "left-0 top-0 w-1 h-full cursor-col-resize");
      case 'top':
        return cn(baseClasses, "bottom-0 left-0 w-full h-1 cursor-row-resize");
      case 'bottom':
        return cn(baseClasses, "top-0 left-0 w-full h-1 cursor-row-resize");
    }
  };

  const getCollapseButtonClasses = () => {
    const baseClasses = "absolute z-20 h-6 w-6 p-0 rounded-sm bg-background border border-border hover:bg-accent transition-colors";
    
    switch (side) {
      case 'left':
        return cn(baseClasses, "right-0 top-1/2 -translate-y-1/2 translate-x-1/2");
      case 'right':
        return cn(baseClasses, "left-0 top-1/2 -translate-y-1/2 -translate-x-1/2");
      case 'top':
        return cn(baseClasses, "bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2");
      case 'bottom':
        return cn(baseClasses, "top-0 left-1/2 -translate-x-1/2 -translate-y-1/2");
    }
  };

  const getPanelStyle = () => {
    if (isCollapsed) {
      return isHorizontal ? { width: 0 } : { height: 0 };
    }
    return isHorizontal ? { width: size } : { height: size };
  };

  const getPanelClasses = () => {
    return cn(
      "relative bg-background border-border overflow-hidden transition-all duration-200",
      {
        'border-r': side === 'left',
        'border-l': side === 'right',
        'border-b': side === 'top',
        'border-t': side === 'bottom',
      },
      className
    );
  };

  return (
    <div
      ref={panelRef}
      className={getPanelClasses()}
      style={getPanelStyle()}
    >
      {/* Resize Handle */}
      {!isCollapsed && (
        <div
          className={getResizeHandleClasses()}
          onMouseDown={handleMouseDown}
        >
          {/* Visual indicator for resize handle */}
          <div className={cn(
            "absolute bg-primary/20 opacity-0 group-hover:opacity-100 transition-opacity",
            isHorizontal ? "inset-y-0 left-0 w-full" : "inset-x-0 top-0 h-full"
          )} />
        </div>
      )}

      {/* Collapse Button */}
      <Button
        variant="ghost"
        size="sm"
        className={getCollapseButtonClasses()}
        onClick={toggleCollapse}
        aria-label={isCollapsed ? 'Expand panel' : 'Collapse panel'}
      >
        <CollapseIcon className="h-3 w-3" />
      </Button>

      {/* Panel Content */}
      <AnimatePresence mode="wait">
        {!isCollapsed && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.15 }}
            className="h-full w-full"
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Collapsed State Indicator */}
      {isCollapsed && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className={cn(
            "absolute bg-muted/50 flex items-center justify-center",
            isHorizontal 
              ? "inset-y-0 left-0 w-8 writing-mode-vertical" 
              : "inset-x-0 top-0 h-8"
          )}
        >
          <span className={cn(
            "text-xs text-muted-foreground font-medium",
            isHorizontal && "transform -rotate-90 whitespace-nowrap"
          )}>
            {side.charAt(0).toUpperCase() + side.slice(1)}
          </span>
        </motion.div>
      )}
    </div>
  );
}
