'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Terminal as TerminalIcon,
  Power,
  Settings,
  Maximize2,
  Minimize2,
  Copy,
  ClipboardPaste as Paste,
  Search,
  RefreshCw,
  Wifi,
  WifiOff,
  Lock,
  Unlock,
  AlertCircle,
  CheckCircle,
  Loader2,
  X,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

import { BaseTerminal, BaseTerminalRef, TERMINAL_THEMES } from './base-terminal';
import { SSHConnectionConfig, SSHConnectionStatus } from '@/types/ssh-docker';
import { useSSHDocker } from '@/hooks/useSSHDocker';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface SSHTerminalProps {
  connectionId?: string;
  connectionConfig?: SSHConnectionConfig;
  className?: string;
  height?: string | number;
  autoConnect?: boolean;
  showToolbar?: boolean;
  showStatusBar?: boolean;
  onConnectionChange?: (status: SSHConnectionStatus | null) => void;
}

export function SSHTerminal({
  connectionId,
  connectionConfig,
  className,
  height = '400px',
  autoConnect = false,
  showToolbar = true,
  showStatusBar = true,
  onConnectionChange,
}: SSHTerminalProps) {
  const terminalRef = useRef<BaseTerminalRef>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<SSHConnectionStatus | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [selectedTheme, setSelectedTheme] = useState<keyof typeof TERMINAL_THEMES>('default');
  const [fontSize, setFontSize] = useState(14);
  const [websocketUrl, setWebsocketUrl] = useState<string | undefined>();

  const { connect, disconnect, getConnectionStatus } = useSSHDocker();

  // Handle connection
  const handleConnect = useCallback(async () => {
    if (!connectionConfig && !connectionId) {
      toast.error('No connection configuration provided');
      return;
    }

    setIsConnecting(true);
    try {
      if (connectionConfig) {
        await connect(connectionConfig);
        setConnectionStatus({
          id: connectionConfig.id,
          connected: true,
          dockerAvailable: true,
          lastConnected: new Date(),
        });
      }

      // Set up WebSocket URL for terminal communication
      const wsUrl = `ws://localhost:3000/api/ssh-terminal/${connectionId || connectionConfig?.id}`;
      setWebsocketUrl(wsUrl);
      setIsConnected(true);
      
      toast.success('SSH connection established');
    } catch (error) {
      toast.error(`Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setConnectionStatus({
        id: connectionId || connectionConfig?.id || 'unknown',
        connected: false,
        error: error instanceof Error ? error.message : 'Connection failed',
        lastConnected: new Date(),
      });
    } finally {
      setIsConnecting(false);
    }
  }, [connectionConfig, connectionId, connect]);

  // Handle disconnect
  const handleDisconnect = useCallback(async () => {
    if (!connectionId && !connectionConfig?.id) return;

    try {
      await disconnect(connectionId || connectionConfig!.id);
      setIsConnected(false);
      setWebsocketUrl(undefined);
      setConnectionStatus(null);
      terminalRef.current?.clear();
      
      toast.success('SSH connection closed');
    } catch (error) {
      toast.error(`Disconnect failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [connectionId, connectionConfig, disconnect]);

  // Auto connect on mount
  useEffect(() => {
    if (autoConnect && (connectionConfig || connectionId)) {
      handleConnect();
    }
  }, [autoConnect, connectionConfig, connectionId, handleConnect]);

  // Update connection status
  useEffect(() => {
    onConnectionChange?.(connectionStatus);
  }, [connectionStatus, onConnectionChange]);

  // Handle terminal data
  const handleTerminalData = useCallback((data: string) => {
    // Send data to WebSocket (SSH session)
    // This would be handled by the WebSocket connection in the BaseTerminal
    console.log('Terminal data:', data);
  }, []);

  // Handle search
  const handleSearch = useCallback((direction: 'next' | 'previous') => {
    if (!searchTerm.trim() || !terminalRef.current) return;

    const found = direction === 'next' 
      ? terminalRef.current.findNext(searchTerm)
      : terminalRef.current.findPrevious(searchTerm);

    if (!found) {
      toast.info('No more matches found');
    }
  }, [searchTerm]);

  // Handle copy
  const handleCopy = useCallback(() => {
    const selection = terminalRef.current?.getSelection();
    if (selection) {
      navigator.clipboard.writeText(selection);
      toast.success('Copied to clipboard');
    }
  }, []);

  // Handle paste
  const handlePaste = useCallback(async () => {
    try {
      const text = await navigator.clipboard.readText();
      terminalRef.current?.write(text);
    } catch (error) {
      toast.error('Failed to paste from clipboard');
    }
  }, []);

  // Handle theme change
  const handleThemeChange = useCallback((theme: keyof typeof TERMINAL_THEMES) => {
    setSelectedTheme(theme);
    // Terminal theme is applied via options prop
  }, []);

  // Handle font size change
  const handleFontSizeChange = useCallback((size: number) => {
    setFontSize(size);
  }, []);

  const getConnectionIcon = () => {
    if (isConnecting) return <Loader2 className="h-4 w-4 animate-spin" />;
    if (isConnected) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (connectionStatus?.error) return <AlertCircle className="h-4 w-4 text-red-600" />;
    return <WifiOff className="h-4 w-4 text-gray-600" />;
  };

  const getConnectionText = () => {
    if (isConnecting) return 'Connecting...';
    if (isConnected) return 'Connected';
    if (connectionStatus?.error) return 'Connection Failed';
    return 'Disconnected';
  };

  const terminalOptions = {
    theme: TERMINAL_THEMES[selectedTheme],
    fontSize,
    cursorBlink: true,
    scrollback: 10000,
  };

  return (
    <Card className={cn('flex flex-col', className)}>
      {showToolbar && (
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TerminalIcon className="h-5 w-5" />
              <CardTitle className="text-base">
                SSH Terminal {connectionConfig?.name && `- ${connectionConfig.name}`}
              </CardTitle>
              <Badge variant="outline" className="flex items-center gap-1">
                {getConnectionIcon()}
                {getConnectionText()}
              </Badge>
            </div>

            <div className="flex items-center gap-1">
              {/* Search */}
              <Dialog open={showSearch} onOpenChange={setShowSearch}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Search className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Search Terminal</DialogTitle>
                    <DialogDescription>
                      Search for text in the terminal output
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <Input
                      placeholder="Search term..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleSearch('next');
                        }
                      }}
                    />
                    <div className="flex gap-2">
                      <Button onClick={() => handleSearch('previous')} disabled={!searchTerm.trim()}>
                        Previous
                      </Button>
                      <Button onClick={() => handleSearch('next')} disabled={!searchTerm.trim()}>
                        Next
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>

              {/* Copy/Paste */}
              <Button variant="ghost" size="icon" onClick={handleCopy}>
                <Copy className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={handlePaste}>
                <Paste className="h-4 w-4" />
              </Button>

              <Separator orientation="vertical" className="h-6" />

              {/* Settings */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Settings className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <div className="p-2">
                    <label className="text-sm font-medium">Theme</label>
                    <Select value={selectedTheme} onValueChange={handleThemeChange}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.keys(TERMINAL_THEMES).map((theme) => (
                          <SelectItem key={theme} value={theme}>
                            {theme.charAt(0).toUpperCase() + theme.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <label className="text-sm font-medium">Font Size</label>
                    <Select value={fontSize.toString()} onValueChange={(value) => handleFontSizeChange(parseInt(value))}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[10, 12, 14, 16, 18, 20, 24].map((size) => (
                          <SelectItem key={size} value={size.toString()}>
                            {size}px
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => terminalRef.current?.clear()}>
                    Clear Terminal
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Fullscreen */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsFullscreen(!isFullscreen)}
              >
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </Button>

              {/* Connection toggle */}
              {isConnected ? (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleDisconnect}
                  className="text-red-600 hover:text-red-700"
                >
                  <Power className="h-4 w-4" />
                </Button>
              ) : (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleConnect}
                  disabled={isConnecting || (!connectionConfig && !connectionId)}
                  className="text-green-600 hover:text-green-700"
                >
                  <Power className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      )}

      <CardContent className="flex-1 p-0">
        <div
          className={cn(
            'relative',
            isFullscreen && 'fixed inset-0 z-50 bg-background'
          )}
          style={{ height: isFullscreen ? '100vh' : height }}
        >
          <BaseTerminal
            ref={terminalRef}
            className="h-full"
            options={terminalOptions}
            onData={handleTerminalData}
            websocketUrl={websocketUrl}
            enableWebLinks
            enableSearch
            enableFit
            autoFocus={isConnected}
          />

          {/* Connection overlay */}
          <AnimatePresence>
            {!isConnected && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-background/90 flex items-center justify-center"
              >
                <div className="text-center">
                  {isConnecting ? (
                    <>
                      <Loader2 className="h-8 w-8 mx-auto mb-4 animate-spin" />
                      <p className="text-lg font-medium">Connecting to SSH server...</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        {connectionConfig?.host || connectionId}
                      </p>
                    </>
                  ) : connectionStatus?.error ? (
                    <>
                      <AlertCircle className="h-8 w-8 mx-auto mb-4 text-red-600" />
                      <p className="text-lg font-medium text-red-600">Connection Failed</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        {connectionStatus.error}
                      </p>
                      <Button onClick={handleConnect} className="mt-4">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Retry Connection
                      </Button>
                    </>
                  ) : (
                    <>
                      <TerminalIcon className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                      <p className="text-lg font-medium">SSH Terminal</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Click connect to start a terminal session
                      </p>
                      <Button 
                        onClick={handleConnect} 
                        className="mt-4"
                        disabled={!connectionConfig && !connectionId}
                      >
                        <Wifi className="h-4 w-4 mr-2" />
                        Connect
                      </Button>
                    </>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Fullscreen close button */}
          {isFullscreen && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-4 right-4"
              onClick={() => setIsFullscreen(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardContent>

      {showStatusBar && (
        <div className="border-t p-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              {connectionConfig && (
                <span>
                  {connectionConfig.username}@{connectionConfig.host}:{connectionConfig.port}
                </span>
              )}
              {isConnected && (
                <span className="flex items-center gap-1">
                  <Lock className="h-3 w-3" />
                  SSH Encrypted
                </span>
              )}
            </div>
            <div className="flex items-center gap-4">
              <span>Theme: {selectedTheme}</span>
              <span>Font: {fontSize}px</span>
              {connectionStatus?.lastConnected && (
                <span>
                  Last: {connectionStatus.lastConnected.toLocaleTimeString()}
                </span>
              )}
            </div>
          </div>
        </div>
      )}
    </Card>
  );
}