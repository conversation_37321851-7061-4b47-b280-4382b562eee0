'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface TerminalLayoutProps {
  children: React.ReactNode;
  className?: string;
  fullscreen?: boolean;
}

export function TerminalLayout({ 
  children, 
  className, 
  fullscreen = false 
}: TerminalLayoutProps) {
  return (
    <div
      className={cn(
        'terminal-layout',
        fullscreen && 'fixed inset-0 z-50 bg-background',
        className
      )}
    >
      {children}
    </div>
  );
}

interface TerminalContainerProps {
  children: React.ReactNode;
  className?: string;
  height?: string | number;
}

export function TerminalContainer({ 
  children, 
  className, 
  height = '400px' 
}: TerminalContainerProps) {
  return (
    <div
      className={cn(
        'terminal-container relative overflow-hidden rounded-lg border bg-card',
        className
      )}
      style={{ height }}
    >
      {children}
    </div>
  );
}

interface TerminalHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export function TerminalHeader({ children, className }: TerminalHeaderProps) {
  return (
    <div className={cn('terminal-header border-b p-2', className)}>
      {children}
    </div>
  );
}

interface TerminalContentProps {
  children: React.ReactNode;
  className?: string;
}

export function TerminalContent({ children, className }: TerminalContentProps) {
  return (
    <div className={cn('terminal-content flex-1 overflow-hidden', className)}>
      {children}
    </div>
  );
}

interface TerminalFooterProps {
  children: React.ReactNode;
  className?: string;
}

export function TerminalFooter({ children, className }: TerminalFooterProps) {
  return (
    <div className={cn('terminal-footer border-t p-2', className)}>
      {children}
    </div>
  );
}