'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Terminal as TerminalIcon,
  Play,
  Square,
  Trash2,
  RefreshCw,
  Download,
  Upload,
  Settings,
  Maximize2,
  Minimize2,
  Copy,
  ClipboardPaste as Paste,
  HardDrive,
  Container,
  Image,
  Network,
  Volume,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  X,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { BaseTerminal, BaseTerminalRef, TERMINAL_THEMES } from './base-terminal';
import { ContainerInfo } from '@/types/docker';
import { useDocker } from '@/hooks/useDocker';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface DockerTerminalProps {
  containerId?: string;
  containerName?: string;
  className?: string;
  height?: string | number;
  autoConnect?: boolean;
  showToolbar?: boolean;
  showStatusBar?: boolean;
  defaultShell?: string;
  onContainerChange?: (container: ContainerInfo | null) => void;
}

export function DockerTerminal({
  containerId,
  containerName,
  className,
  height = '400px',
  autoConnect = false,
  showToolbar = true,
  showStatusBar = true,
  defaultShell = '/bin/bash',
  onContainerChange,
}: DockerTerminalProps) {
  const terminalRef = useRef<BaseTerminalRef>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [selectedContainer, setSelectedContainer] = useState<ContainerInfo | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedTheme, setSelectedTheme] = useState<keyof typeof TERMINAL_THEMES>('default');
  const [fontSize, setFontSize] = useState(14);
  const [currentShell, setCurrentShell] = useState(defaultShell);
  const [websocketUrl, setWebsocketUrl] = useState<string | undefined>();

  const { containers, getContainer, execContainer, connected: dockerConnected } = useDocker();

  // Find container by ID or name
  useEffect(() => {
    if (containerId || containerName) {
      const container = containers.find(c => 
        c.id === containerId || c.name === containerName
      );
      setSelectedContainer(container || null);
      onContainerChange?.(container || null);
    }
  }, [containerId, containerName, containers, onContainerChange]);

  // Handle container selection
  const handleContainerSelect = useCallback((newContainerId: string) => {
    const container = containers.find(c => c.id === newContainerId);
    setSelectedContainer(container || null);
    setIsConnected(false);
    setWebsocketUrl(undefined);
    onContainerChange?.(container || null);
  }, [containers, onContainerChange]);

  // Handle connection to container
  const handleConnect = useCallback(async () => {
    if (!selectedContainer) {
      toast.error('No container selected');
      return;
    }

    if (selectedContainer.status !== 'running') {
      toast.error('Container must be running to connect');
      return;
    }

    setIsConnecting(true);
    try {
      // Set up WebSocket URL for container exec session
      const wsUrl = `ws://localhost:3000/api/docker/containers/${selectedContainer.id}/exec?shell=${encodeURIComponent(currentShell)}`;
      setWebsocketUrl(wsUrl);
      setIsConnected(true);
      
      // Clear terminal and show connection message
      terminalRef.current?.clear();
      terminalRef.current?.writeln(`\x1b[32mConnected to container: ${selectedContainer.name}\x1b[0m`);
      terminalRef.current?.writeln(`\x1b[36mShell: ${currentShell}\x1b[0m`);
      terminalRef.current?.writeln(`\x1b[33mContainer ID: ${selectedContainer.id}\x1b[0m`);
      terminalRef.current?.writeln('');
      
      toast.success(`Connected to container: ${selectedContainer.name}`);
    } catch (error) {
      toast.error(`Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsConnecting(false);
    }
  }, [selectedContainer, currentShell]);

  // Handle disconnect
  const handleDisconnect = useCallback(() => {
    setIsConnected(false);
    setWebsocketUrl(undefined);
    terminalRef.current?.writeln('\x1b[31mDisconnected from container\x1b[0m');
    toast.success('Disconnected from container');
  }, []);

  // Auto connect on mount
  useEffect(() => {
    if (autoConnect && selectedContainer && selectedContainer.status === 'running') {
      handleConnect();
    }
  }, [autoConnect, selectedContainer, handleConnect]);

  // Handle terminal data
  const handleTerminalData = useCallback((data: string) => {
    // Data is handled by WebSocket connection in BaseTerminal
    console.log('Terminal data:', data);
  }, []);

  // Quick actions
  const handleQuickCommand = useCallback((command: string) => {
    if (!isConnected) {
      toast.error('Not connected to container');
      return;
    }
    terminalRef.current?.write(command + '\n');
  }, [isConnected]);

  // Handle copy
  const handleCopy = useCallback(() => {
    const selection = terminalRef.current?.getSelection();
    if (selection) {
      navigator.clipboard.writeText(selection);
      toast.success('Copied to clipboard');
    }
  }, []);

  // Handle paste
  const handlePaste = useCallback(async () => {
    try {
      const text = await navigator.clipboard.readText();
      terminalRef.current?.write(text);
    } catch (error) {
      toast.error('Failed to paste from clipboard');
    }
  }, []);

  const getContainerStatusIcon = (status?: string) => {
    switch (status) {
      case 'running': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'stopped': return <XCircle className="h-4 w-4 text-red-600" />;
      case 'starting': return <Loader2 className="h-4 w-4 animate-spin text-yellow-600" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getConnectionStatus = () => {
    if (isConnecting) return { icon: <Loader2 className="h-4 w-4 animate-spin" />, text: 'Connecting...' };
    if (isConnected) return { icon: <CheckCircle className="h-4 w-4 text-green-600" />, text: 'Connected' };
    if (!dockerConnected) return { icon: <XCircle className="h-4 w-4 text-red-600" />, text: 'Docker Unavailable' };
    if (!selectedContainer) return { icon: <AlertTriangle className="h-4 w-4 text-yellow-600" />, text: 'No Container' };
    if (selectedContainer.status !== 'running') return { icon: <XCircle className="h-4 w-4 text-orange-600" />, text: 'Container Stopped' };
    return { icon: <XCircle className="h-4 w-4 text-gray-600" />, text: 'Disconnected' };
  };

  const terminalOptions = {
    theme: TERMINAL_THEMES[selectedTheme],
    fontSize,
    cursorBlink: true,
    scrollback: 10000,
  };

  const connectionStatus = getConnectionStatus();

  return (
    <Card className={cn('flex flex-col', className)}>
      {showToolbar && (
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Container className="h-5 w-5" />
              <CardTitle className="text-base">Docker Terminal</CardTitle>
              <Badge variant="outline" className="flex items-center gap-1">
                {connectionStatus.icon}
                {connectionStatus.text}
              </Badge>
            </div>

            <div className="flex items-center gap-1">
              {/* Container selector */}
              <Select
                value={selectedContainer?.id || ''}
                onValueChange={handleContainerSelect}
              >
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select container..." />
                </SelectTrigger>
                <SelectContent>
                  {containers.map((container) => (
                    <SelectItem key={container.id} value={container.id}>
                      <div className="flex items-center gap-2">
                        {getContainerStatusIcon(container.status)}
                        <span>{container.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Separator orientation="vertical" className="h-6" />

              {/* Shell selector */}
              <Select value={currentShell} onValueChange={setCurrentShell}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="/bin/bash">bash</SelectItem>
                  <SelectItem value="/bin/sh">sh</SelectItem>
                  <SelectItem value="/bin/zsh">zsh</SelectItem>
                  <SelectItem value="/bin/fish">fish</SelectItem>
                </SelectContent>
              </Select>

              <Separator orientation="vertical" className="h-6" />

              {/* Copy/Paste */}
              <Button variant="ghost" size="icon" onClick={handleCopy}>
                <Copy className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={handlePaste}>
                <Paste className="h-4 w-4" />
              </Button>

              {/* Quick commands */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <TerminalIcon className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleQuickCommand('ls -la')}>
                    List files (ls -la)
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleQuickCommand('ps aux')}>
                    Show processes (ps aux)
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleQuickCommand('df -h')}>
                    Disk usage (df -h)
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleQuickCommand('free -m')}>
                    Memory usage (free -m)
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleQuickCommand('top')}>
                    System monitor (top)
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => handleQuickCommand('env')}>
                    Environment variables
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleQuickCommand('pwd')}>
                    Current directory
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Settings */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Settings className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <div className="p-2">
                    <label className="text-sm font-medium">Theme</label>
                    <Select value={selectedTheme} onValueChange={setSelectedTheme}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.keys(TERMINAL_THEMES).map((theme) => (
                          <SelectItem key={theme} value={theme}>
                            {theme.charAt(0).toUpperCase() + theme.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <label className="text-sm font-medium">Font Size</label>
                    <Select value={fontSize.toString()} onValueChange={(value) => setFontSize(parseInt(value))}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[10, 12, 14, 16, 18, 20, 24].map((size) => (
                          <SelectItem key={size} value={size.toString()}>
                            {size}px
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => terminalRef.current?.clear()}>
                    Clear Terminal
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Fullscreen */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsFullscreen(!isFullscreen)}
              >
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </Button>

              {/* Connection toggle */}
              {isConnected ? (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleDisconnect}
                  className="text-red-600 hover:text-red-700"
                >
                  <Square className="h-4 w-4" />
                </Button>
              ) : (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleConnect}
                  disabled={isConnecting || !selectedContainer || selectedContainer.status !== 'running'}
                  className="text-green-600 hover:text-green-700"
                >
                  <Play className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      )}

      <CardContent className="flex-1 p-0">
        <div
          className={cn(
            'relative',
            isFullscreen && 'fixed inset-0 z-50 bg-background'
          )}
          style={{ height: isFullscreen ? '100vh' : height }}
        >
          <BaseTerminal
            ref={terminalRef}
            className="h-full"
            options={terminalOptions}
            onData={handleTerminalData}
            websocketUrl={websocketUrl}
            enableWebLinks
            enableSearch
            enableFit
            autoFocus={isConnected}
          />

          {/* Connection overlay */}
          <AnimatePresence>
            {!isConnected && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-background/90 flex items-center justify-center"
              >
                <div className="text-center">
                  {isConnecting ? (
                    <>
                      <Loader2 className="h-8 w-8 mx-auto mb-4 animate-spin" />
                      <p className="text-lg font-medium">Connecting to container...</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        {selectedContainer?.name}
                      </p>
                    </>
                  ) : !dockerConnected ? (
                    <>
                      <XCircle className="h-8 w-8 mx-auto mb-4 text-red-600" />
                      <p className="text-lg font-medium text-red-600">Docker Unavailable</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Cannot connect to Docker daemon
                      </p>
                    </>
                  ) : !selectedContainer ? (
                    <>
                      <Container className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                      <p className="text-lg font-medium">No Container Selected</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Select a container to start a terminal session
                      </p>
                    </>
                  ) : selectedContainer.status !== 'running' ? (
                    <>
                      <AlertTriangle className="h-8 w-8 mx-auto mb-4 text-orange-600" />
                      <p className="text-lg font-medium text-orange-600">Container Not Running</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Container must be started to connect
                      </p>
                    </>
                  ) : (
                    <>
                      <TerminalIcon className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                      <p className="text-lg font-medium">Docker Terminal</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Ready to connect to {selectedContainer.name}
                      </p>
                      <Button onClick={handleConnect} className="mt-4">
                        <Play className="h-4 w-4 mr-2" />
                        Connect
                      </Button>
                    </>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Fullscreen close button */}
          {isFullscreen && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-4 right-4"
              onClick={() => setIsFullscreen(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardContent>

      {showStatusBar && (
        <div className="border-t p-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              {selectedContainer && (
                <>
                  <span>Container: {selectedContainer.name}</span>
                  <span>Image: {selectedContainer.image}</span>
                  <span>Status: {selectedContainer.status}</span>
                </>
              )}
              {isConnected && (
                <span className="flex items-center gap-1">
                  <HardDrive className="h-3 w-3" />
                  Shell: {currentShell}
                </span>
              )}
            </div>
            <div className="flex items-center gap-4">
              <span>Theme: {selectedTheme}</span>
              <span>Font: {fontSize}px</span>
              {selectedContainer && (
                <span>
                  Created: {selectedContainer.created instanceof Date 
                    ? selectedContainer.created.toLocaleDateString() 
                    : selectedContainer.created}
                </span>
              )}
            </div>
          </div>
        </div>
      )}
    </Card>
  );
}