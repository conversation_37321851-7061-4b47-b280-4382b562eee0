'use client';

import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import { SearchAddon } from '@xterm/addon-search';
import { AttachAddon } from '@xterm/addon-attach';
import { WebglAddon } from '@xterm/addon-webgl';

// Import XTerm CSS
import '@xterm/xterm/css/xterm.css';

export interface TerminalTheme {
  foreground?: string;
  background?: string;
  cursor?: string;
  cursorAccent?: string;
  selectionBackground?: string;
  black?: string;
  red?: string;
  green?: string;
  yellow?: string;
  blue?: string;
  magenta?: string;
  cyan?: string;
  white?: string;
  brightBlack?: string;
  brightRed?: string;
  brightGreen?: string;
  brightYellow?: string;
  brightBlue?: string;
  brightMagenta?: string;
  brightCyan?: string;
  brightWhite?: string;
}

export interface BaseTerminalOptions {
  cols?: number;
  rows?: number;
  cursorBlink?: boolean;
  cursorStyle?: 'block' | 'underline' | 'bar';
  fontSize?: number;
  fontFamily?: string;
  theme?: TerminalTheme;
  scrollback?: number;
  tabStopWidth?: number;
  disableStdin?: boolean;
  bellStyle?: 'none' | 'sound' | 'visual' | 'both';
  macOptionIsMeta?: boolean;
  rightClickSelectsWord?: boolean;
  rendererType?: 'dom' | 'canvas' | 'webgl';
  smoothScrollDuration?: number;
  allowTransparency?: boolean;
}

export interface BaseTerminalProps {
  className?: string;
  style?: React.CSSProperties;
  options?: BaseTerminalOptions;
  onData?: (data: string) => void;
  onResize?: (cols: number, rows: number) => void;
  onBinary?: (data: string) => void;
  onCursorMove?: () => void;
  onKey?: (key: { key: string; domEvent: KeyboardEvent }) => void;
  onLineFeed?: () => void;
  onScroll?: (ydisp: number) => void;
  onSelectionChange?: () => void;
  onRender?: (e: { start: number; end: number }) => void;
  onTitleChange?: (title: string) => void;
  enableWebLinks?: boolean;
  enableSearch?: boolean;
  enableFit?: boolean;
  enableWebGL?: boolean;
  websocketUrl?: string;
  autoFocus?: boolean;
}

export interface BaseTerminalRef {
  terminal: Terminal | null;
  focus: () => void;
  blur: () => void;
  clear: () => void;
  write: (data: string) => void;
  writeln: (data: string) => void;
  fit: () => void;
  scrollToTop: () => void;
  scrollToBottom: () => void;
  scrollToLine: (line: number) => void;
  selectAll: () => void;
  clearSelection: () => void;
  getSelection: () => string;
  findNext: (term: string) => boolean;
  findPrevious: (term: string) => boolean;
  dispose: () => void;
  attachWebSocket: (url: string) => void;
  detachWebSocket: () => void;
}

// Default themes
export const TERMINAL_THEMES = {
  default: {
    foreground: '#d0d0d0',
    background: '#1e1e1e',
    cursor: '#aeafad',
    black: '#000000',
    red: '#cd3131',
    green: '#0dbc79',
    yellow: '#e5e510',
    blue: '#2472c8',
    magenta: '#bc3fbc',
    cyan: '#11a8cd',
    white: '#e5e5e5',
    brightBlack: '#666666',
    brightRed: '#f14c4c',
    brightGreen: '#23d18b',
    brightYellow: '#f5f543',
    brightBlue: '#3b8eea',
    brightMagenta: '#d670d6',
    brightCyan: '#29b8db',
    brightWhite: '#e5e5e5',
  },
  dark: {
    foreground: '#f8f8f2',
    background: '#282a36',
    cursor: '#f8f8f0',
    black: '#21222c',
    red: '#ff5555',
    green: '#50fa7b',
    yellow: '#f1fa8c',
    blue: '#bd93f9',
    magenta: '#ff79c6',
    cyan: '#8be9fd',
    white: '#f8f8f2',
    brightBlack: '#6272a4',
    brightRed: '#ff6e6e',
    brightGreen: '#69ff94',
    brightYellow: '#ffffa5',
    brightBlue: '#d6acff',
    brightMagenta: '#ff92df',
    brightCyan: '#a4ffff',
    brightWhite: '#ffffff',
  },
  light: {
    foreground: '#383a42',
    background: '#fafafa',
    cursor: '#383a42',
    black: '#383a42',
    red: '#e45649',
    green: '#50a14f',
    yellow: '#c18401',
    blue: '#4078f2',
    magenta: '#a626a4',
    cyan: '#0184bc',
    white: '#fafafa',
    brightBlack: '#4f525e',
    brightRed: '#e06c75',
    brightGreen: '#98c379',
    brightYellow: '#e5c07b',
    brightBlue: '#61afef',
    brightMagenta: '#c678dd',
    brightCyan: '#56b6c2',
    brightWhite: '#ffffff',
  },
  solarizedDark: {
    foreground: '#839496',
    background: '#002b36',
    cursor: '#839496',
    black: '#073642',
    red: '#dc322f',
    green: '#859900',
    yellow: '#b58900',
    blue: '#268bd2',
    magenta: '#d33682',
    cyan: '#2aa198',
    white: '#eee8d5',
    brightBlack: '#586e75',
    brightRed: '#cb4b16',
    brightGreen: '#586e75',
    brightYellow: '#657b83',
    brightBlue: '#839496',
    brightMagenta: '#6c71c4',
    brightCyan: '#93a1a1',
    brightWhite: '#fdf6e3',
  },
};

export const BaseTerminal = forwardRef<BaseTerminalRef, BaseTerminalProps>(
  ({
    className = '',
    style,
    options = {},
    onData,
    onResize,
    onBinary,
    onCursorMove,
    onKey,
    onLineFeed,
    onScroll,
    onSelectionChange,
    onRender,
    onTitleChange,
    enableWebLinks = true,
    enableSearch = true,
    enableFit = true,
    enableWebGL = false,
    websocketUrl,
    autoFocus = true,
  }, ref) => {
    const terminalRef = useRef<HTMLDivElement>(null);
    const terminalInstanceRef = useRef<Terminal | null>(null);
    const fitAddonRef = useRef<FitAddon | null>(null);
    const webLinksAddonRef = useRef<WebLinksAddon | null>(null);
    const searchAddonRef = useRef<SearchAddon | null>(null);
    const attachAddonRef = useRef<AttachAddon | null>(null);
    const webglAddonRef = useRef<WebglAddon | null>(null);
    const [isReady, setIsReady] = useState(false);

    // Initialize terminal
    useEffect(() => {
      if (!terminalRef.current || terminalInstanceRef.current) return;

      const defaultOptions: BaseTerminalOptions = {
        cols: 80,
        rows: 24,
        cursorBlink: true,
        cursorStyle: 'block',
        fontSize: 14,
        fontFamily: '"Cascadia Code", "Fira Code", "SF Mono", Monaco, "Inconsolata", "Roboto Mono", "Source Code Pro", monospace',
        theme: TERMINAL_THEMES.default,
        scrollback: 1000,
        tabStopWidth: 4,
        bellStyle: 'sound',
        macOptionIsMeta: true,
        rightClickSelectsWord: true,
        rendererType: enableWebGL ? 'webgl' : 'canvas',
        smoothScrollDuration: 0,
        allowTransparency: false,
        ...options,
      };

      const terminal = new Terminal(defaultOptions);
      terminalInstanceRef.current = terminal;

      // Add addons
      if (enableFit) {
        const fitAddon = new FitAddon();
        fitAddonRef.current = fitAddon;
        terminal.loadAddon(fitAddon);
      }

      if (enableWebLinks) {
        const webLinksAddon = new WebLinksAddon();
        webLinksAddonRef.current = webLinksAddon;
        terminal.loadAddon(webLinksAddon);
      }

      if (enableSearch) {
        const searchAddon = new SearchAddon();
        searchAddonRef.current = searchAddon;
        terminal.loadAddon(searchAddon);
      }

      if (enableWebGL) {
        try {
          const webglAddon = new WebglAddon();
          webglAddonRef.current = webglAddon;
          terminal.loadAddon(webglAddon);
        } catch (error) {
          console.warn('WebGL addon failed to load, falling back to canvas renderer:', error);
        }
      }

      // Open terminal
      terminal.open(terminalRef.current);

      // Fit terminal to container
      if (fitAddonRef.current) {
        fitAddonRef.current.fit();
      }

      // Set up event listeners
      if (onData) {
        terminal.onData(onData);
      }

      if (onResize) {
        terminal.onResize(({ cols, rows }) => onResize(cols, rows));
      }

      if (onBinary) {
        terminal.onBinary(onBinary);
      }

      if (onCursorMove) {
        terminal.onCursorMove(onCursorMove);
      }

      if (onKey) {
        terminal.onKey(onKey);
      }

      if (onLineFeed) {
        terminal.onLineFeed(onLineFeed);
      }

      if (onScroll) {
        terminal.onScroll(onScroll);
      }

      if (onSelectionChange) {
        terminal.onSelectionChange(onSelectionChange);
      }

      if (onRender) {
        terminal.onRender(onRender);
      }

      if (onTitleChange) {
        terminal.onTitleChange(onTitleChange);
      }

      // Auto focus
      if (autoFocus) {
        setTimeout(() => terminal.focus(), 100);
      }

      setIsReady(true);

      // Cleanup function
      return () => {
        if (attachAddonRef.current) {
          attachAddonRef.current.dispose();
        }
        if (terminalInstanceRef.current) {
          terminalInstanceRef.current.dispose();
          terminalInstanceRef.current = null;
        }
        setIsReady(false);
      };
    }, []);

    // Handle WebSocket connection
    useEffect(() => {
      if (!websocketUrl || !terminalInstanceRef.current || !isReady) return;

      const attachAddon = new AttachAddon(new WebSocket(websocketUrl));
      attachAddonRef.current = attachAddon;
      terminalInstanceRef.current.loadAddon(attachAddon);

      return () => {
        if (attachAddonRef.current) {
          attachAddonRef.current.dispose();
          attachAddonRef.current = null;
        }
      };
    }, [websocketUrl, isReady]);

    // Handle window resize
    useEffect(() => {
      const handleResize = () => {
        if (fitAddonRef.current && terminalInstanceRef.current) {
          fitAddonRef.current.fit();
        }
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, [isReady]);

    // Expose methods via ref
    useImperativeHandle(ref, () => ({
      terminal: terminalInstanceRef.current,
      focus: () => terminalInstanceRef.current?.focus(),
      blur: () => terminalInstanceRef.current?.blur(),
      clear: () => terminalInstanceRef.current?.clear(),
      write: (data: string) => terminalInstanceRef.current?.write(data),
      writeln: (data: string) => terminalInstanceRef.current?.writeln(data),
      fit: () => fitAddonRef.current?.fit(),
      scrollToTop: () => terminalInstanceRef.current?.scrollToTop(),
      scrollToBottom: () => terminalInstanceRef.current?.scrollToBottom(),
      scrollToLine: (line: number) => terminalInstanceRef.current?.scrollToLine(line),
      selectAll: () => terminalInstanceRef.current?.selectAll(),
      clearSelection: () => terminalInstanceRef.current?.clearSelection(),
      getSelection: () => terminalInstanceRef.current?.getSelection() || '',
      findNext: (term: string) => searchAddonRef.current?.findNext(term) || false,
      findPrevious: (term: string) => searchAddonRef.current?.findPrevious(term) || false,
      dispose: () => {
        if (attachAddonRef.current) {
          attachAddonRef.current.dispose();
        }
        if (terminalInstanceRef.current) {
          terminalInstanceRef.current.dispose();
          terminalInstanceRef.current = null;
        }
      },
      attachWebSocket: (url: string) => {
        if (attachAddonRef.current) {
          attachAddonRef.current.dispose();
        }
        if (terminalInstanceRef.current) {
          const attachAddon = new AttachAddon(new WebSocket(url));
          attachAddonRef.current = attachAddon;
          terminalInstanceRef.current.loadAddon(attachAddon);
        }
      },
      detachWebSocket: () => {
        if (attachAddonRef.current) {
          attachAddonRef.current.dispose();
          attachAddonRef.current = null;
        }
      },
    }));

    return (
      <div
        ref={terminalRef}
        className={`xterm-container ${className}`}
        style={{
          width: '100%',
          height: '100%',
          ...style,
        }}
      />
    );
  }
);

BaseTerminal.displayName = 'BaseTerminal';