'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Terminal as TerminalIcon,
  Plus,
  X,
  Settings,
  Maximize2,
  Minimize2,
  Copy,
  ClipboardPaste as Paste,
  Save,
  FolderOpen,
  Grid3X3,
  Columns2,
  Rows2,
  MoreHorizontal,
  Pin,
  PinOff,
  Eye,
  EyeOff,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

import { BaseTerminal, BaseTerminalRef, TERMINAL_THEMES } from './base-terminal';
import { SSHTerminal } from './ssh-terminal';
import { DockerTerminal } from './docker-terminal';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface TerminalTab {
  id: string;
  title: string;
  type: 'local' | 'ssh' | 'docker';
  config?: any;
  isPinned: boolean;
  isActive: boolean;
  ref: React.RefObject<BaseTerminalRef>;
}

interface MultiTerminalProps {
  className?: string;
  height?: string | number;
  defaultTabs?: Partial<TerminalTab>[];
  maxTabs?: number;
  showTabBar?: boolean;
  showToolbar?: boolean;
  allowReorder?: boolean;
  allowSplit?: boolean;
  onTabsChange?: (tabs: TerminalTab[]) => void;
}

type LayoutMode = 'tabs' | 'grid' | 'horizontal' | 'vertical';

export function MultiTerminal({
  className,
  height = '600px',
  defaultTabs = [],
  maxTabs = 10,
  showTabBar = true,
  showToolbar = true,
  allowReorder = true,
  allowSplit = true,
  onTabsChange,
}: MultiTerminalProps) {
  const [tabs, setTabs] = useState<TerminalTab[]>([]);
  const [activeTabId, setActiveTabId] = useState<string | null>(null);
  const [layoutMode, setLayoutMode] = useState<LayoutMode>('tabs');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedTheme, setSelectedTheme] = useState<keyof typeof TERMINAL_THEMES>('default');
  const [fontSize, setFontSize] = useState(14);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newTabType, setNewTabType] = useState<'local' | 'ssh' | 'docker'>('local');
  const [newTabTitle, setNewTabTitle] = useState('');

  // Initialize with default tabs
  useEffect(() => {
    if (defaultTabs.length > 0 && tabs.length === 0) {
      const initialTabs: TerminalTab[] = defaultTabs.map((tab, index) => ({
        id: tab.id || `tab-${Date.now()}-${index}`,
        title: tab.title || `Terminal ${index + 1}`,
        type: tab.type || 'local',
        config: tab.config,
        isPinned: tab.isPinned || false,
        isActive: index === 0,
        ref: React.createRef<BaseTerminalRef>(),
      }));
      setTabs(initialTabs);
      setActiveTabId(initialTabs[0]?.id || null);
    }
  }, [defaultTabs, tabs.length]);

  // Notify parent of tab changes
  useEffect(() => {
    onTabsChange?.(tabs);
  }, [tabs, onTabsChange]);

  // Create new tab
  const createTab = useCallback((type: 'local' | 'ssh' | 'docker', title?: string, config?: any) => {
    if (tabs.length >= maxTabs) {
      toast.error(`Maximum ${maxTabs} tabs allowed`);
      return;
    }

    const newTab: TerminalTab = {
      id: `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: title || `${type === 'local' ? 'Terminal' : type.toUpperCase()} ${tabs.length + 1}`,
      type,
      config,
      isPinned: false,
      isActive: true,
      ref: React.createRef<BaseTerminalRef>(),
    };

    setTabs(prev => prev.map(t => ({ ...t, isActive: false })).concat(newTab));
    setActiveTabId(newTab.id);
    setShowAddDialog(false);
    setNewTabTitle('');
    
    toast.success(`Created ${type} terminal`);
  }, [tabs.length, maxTabs]);

  // Close tab
  const closeTab = useCallback((tabId: string) => {
    setTabs(prev => {
      const tabToClose = prev.find(t => t.id === tabId);
      if (tabToClose?.isPinned) {
        toast.error('Cannot close pinned tab');
        return prev;
      }

      const newTabs = prev.filter(t => t.id !== tabId);
      
      // If closing active tab, activate another
      if (activeTabId === tabId && newTabs.length > 0) {
        const nextActiveTab = newTabs[newTabs.length - 1];
        nextActiveTab.isActive = true;
        setActiveTabId(nextActiveTab.id);
      } else if (newTabs.length === 0) {
        setActiveTabId(null);
      }

      return newTabs;
    });
  }, [activeTabId]);

  // Switch to tab
  const switchToTab = useCallback((tabId: string) => {
    setTabs(prev => prev.map(t => ({
      ...t,
      isActive: t.id === tabId
    })));
    setActiveTabId(tabId);
  }, []);

  // Toggle pin tab
  const togglePinTab = useCallback((tabId: string) => {
    setTabs(prev => prev.map(t => 
      t.id === tabId ? { ...t, isPinned: !t.isPinned } : t
    ));
  }, []);

  // Rename tab
  const renameTab = useCallback((tabId: string, newTitle: string) => {
    setTabs(prev => prev.map(t => 
      t.id === tabId ? { ...t, title: newTitle } : t
    ));
  }, []);

  // Handle add new tab
  const handleAddTab = useCallback(() => {
    if (newTabTitle.trim()) {
      createTab(newTabType, newTabTitle.trim());
    } else {
      createTab(newTabType);
    }
  }, [newTabType, newTabTitle, createTab]);

  // Quick actions for all terminals
  const handleGlobalCopy = useCallback(() => {
    const activeTab = tabs.find(t => t.id === activeTabId);
    if (activeTab?.ref.current) {
      const selection = activeTab.ref.current.getSelection();
      if (selection) {
        navigator.clipboard.writeText(selection);
        toast.success('Copied to clipboard');
      }
    }
  }, [tabs, activeTabId]);

  const handleGlobalPaste = useCallback(async () => {
    const activeTab = tabs.find(t => t.id === activeTabId);
    if (activeTab?.ref.current) {
      try {
        const text = await navigator.clipboard.readText();
        activeTab.ref.current.write(text);
      } catch (error) {
        toast.error('Failed to paste from clipboard');
      }
    }
  }, [tabs, activeTabId]);

  const handleClearAll = useCallback(() => {
    tabs.forEach(tab => {
      tab.ref.current?.clear();
    });
    toast.success('Cleared all terminals');
  }, [tabs]);

  // Render terminal content based on type
  const renderTerminalContent = (tab: TerminalTab) => {
    const commonProps = {
      height: '100%',
      showToolbar: false,
      showStatusBar: false,
    };

    switch (tab.type) {
      case 'ssh':
        return (
          <SSHTerminal
            {...commonProps}
            connectionConfig={tab.config?.connectionConfig}
            connectionId={tab.config?.connectionId}
            autoConnect={tab.config?.autoConnect}
          />
        );
      case 'docker':
        return (
          <DockerTerminal
            {...commonProps}
            containerId={tab.config?.containerId}
            containerName={tab.config?.containerName}
            autoConnect={tab.config?.autoConnect}
            defaultShell={tab.config?.defaultShell}
          />
        );
      default:
        return (
          <BaseTerminal
            ref={tab.ref}
            className="h-full"
            options={{
              theme: TERMINAL_THEMES[selectedTheme],
              fontSize,
              cursorBlink: true,
              scrollback: 10000,
            }}
            enableWebLinks
            enableSearch
            enableFit
            autoFocus={tab.isActive}
          />
        );
    }
  };

  // Render layout based on mode
  const renderLayout = () => {
    const activeTabs = tabs.filter(tab => layoutMode === 'tabs' ? tab.isActive : true);

    if (layoutMode === 'tabs') {
      const activeTab = tabs.find(t => t.isActive);
      return activeTab ? (
        <div key={activeTab.id} className="h-full">
          {renderTerminalContent(activeTab)}
        </div>
      ) : null;
    }

    if (layoutMode === 'grid') {
      const cols = Math.ceil(Math.sqrt(activeTabs.length));
      return (
        <div 
          className="grid h-full gap-1"
          style={{ 
            gridTemplateColumns: `repeat(${cols}, 1fr)`,
            gridTemplateRows: `repeat(${Math.ceil(activeTabs.length / cols)}, 1fr)`
          }}
        >
          {activeTabs.map(tab => (
            <div key={tab.id} className="border rounded">
              <div className="text-xs p-1 bg-muted border-b">
                {tab.title}
              </div>
              <div style={{ height: 'calc(100% - 24px)' }}>
                {renderTerminalContent(tab)}
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (layoutMode === 'horizontal') {
      return (
        <div className="flex h-full gap-1">
          {activeTabs.map(tab => (
            <div key={tab.id} className="flex-1 border rounded">
              <div className="text-xs p-1 bg-muted border-b">
                {tab.title}
              </div>
              <div style={{ height: 'calc(100% - 24px)' }}>
                {renderTerminalContent(tab)}
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (layoutMode === 'vertical') {
      return (
        <div className="flex flex-col h-full gap-1">
          {activeTabs.map(tab => (
            <div key={tab.id} className="flex-1 border rounded">
              <div className="text-xs p-1 bg-muted border-b">
                {tab.title}
              </div>
              <div style={{ height: 'calc(100% - 24px)' }}>
                {renderTerminalContent(tab)}
              </div>
            </div>
          ))}
        </div>
      );
    }

    return null;
  };

  return (
    <Card className={cn('flex flex-col', className)}>
      {showToolbar && (
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TerminalIcon className="h-5 w-5" />
              <CardTitle className="text-base">Multi Terminal</CardTitle>
              <Badge variant="outline">
                {tabs.length} terminal{tabs.length !== 1 ? 's' : ''}
              </Badge>
            </div>

            <div className="flex items-center gap-1">
              {/* Layout mode selector */}
              {allowSplit && (
                <Select value={layoutMode} onValueChange={(value: LayoutMode) => setLayoutMode(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tabs">
                      <div className="flex items-center gap-2">
                        <TerminalIcon className="h-4 w-4" />
                        Tabs
                      </div>
                    </SelectItem>
                    <SelectItem value="grid">
                      <div className="flex items-center gap-2">
                        <Grid3X3 className="h-4 w-4" />
                        Grid
                      </div>
                    </SelectItem>
                    <SelectItem value="horizontal">
                      <div className="flex items-center gap-2">
                        <Columns2 className="h-4 w-4" />
                        Horizontal
                      </div>
                    </SelectItem>
                    <SelectItem value="vertical">
                      <div className="flex items-center gap-2">
                        <Rows2 className="h-4 w-4" />
                        Vertical
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              )}

              <Separator orientation="vertical" className="h-6" />

              {/* Global actions */}
              <Button variant="ghost" size="icon" onClick={handleGlobalCopy}>
                <Copy className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={handleGlobalPaste}>
                <Paste className="h-4 w-4" />
              </Button>

              {/* Settings */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Settings className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <div className="p-2">
                    <label className="text-sm font-medium">Theme</label>
                    <Select value={selectedTheme} onValueChange={setSelectedTheme}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.keys(TERMINAL_THEMES).map((theme) => (
                          <SelectItem key={theme} value={theme}>
                            {theme.charAt(0).toUpperCase() + theme.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <label className="text-sm font-medium">Font Size</label>
                    <Select value={fontSize.toString()} onValueChange={(value) => setFontSize(parseInt(value))}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[10, 12, 14, 16, 18, 20, 24].map((size) => (
                          <SelectItem key={size} value={size.toString()}>
                            {size}px
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleClearAll}>
                    Clear All Terminals
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Fullscreen */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsFullscreen(!isFullscreen)}
              >
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </Button>

              {/* Add terminal */}
              <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Terminal
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Terminal</DialogTitle>
                    <DialogDescription>
                      Create a new terminal session
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Terminal Type</label>
                      <Select value={newTabType} onValueChange={(value: 'local' | 'ssh' | 'docker') => setNewTabType(value)}>
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="local">Local Terminal</SelectItem>
                          <SelectItem value="ssh">SSH Terminal</SelectItem>
                          <SelectItem value="docker">Docker Terminal</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Title (optional)</label>
                      <Input
                        placeholder="Terminal title..."
                        value={newTabTitle}
                        onChange={(e) => setNewTabTitle(e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="flex gap-2 justify-end">
                      <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleAddTab}>
                        Create Terminal
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
      )}

      {/* Tab bar */}
      {showTabBar && layoutMode === 'tabs' && tabs.length > 0 && (
        <div className="border-b">
          <div className="flex items-center overflow-x-auto">
            {tabs.map((tab) => (
              <div
                key={tab.id}
                className={cn(
                  'flex items-center gap-2 px-3 py-2 border-r cursor-pointer transition-colors',
                  tab.isActive ? 'bg-muted' : 'hover:bg-muted/50'
                )}
                onClick={() => switchToTab(tab.id)}
              >
                <span className="text-sm font-medium truncate max-w-32">
                  {tab.title}
                </span>
                <div className="flex items-center gap-1">
                  {tab.isPinned && <Pin className="h-3 w-3" />}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => renameTab(tab.id, prompt('New title:', tab.title) || tab.title)}>
                        Rename
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => togglePinTab(tab.id)}>
                        {tab.isPinned ? 'Unpin' : 'Pin'}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => closeTab(tab.id)}
                        disabled={tab.isPinned}
                        className="text-red-600"
                      >
                        Close
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  {!tab.isPinned && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={(e) => {
                        e.stopPropagation();
                        closeTab(tab.id);
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <CardContent className="flex-1 p-0">
        <div
          className={cn(
            'relative',
            isFullscreen && 'fixed inset-0 z-50 bg-background'
          )}
          style={{ height: isFullscreen ? '100vh' : height }}
        >
          {tabs.length > 0 ? renderLayout() : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <TerminalIcon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-lg font-medium mb-2">No Terminals</p>
                <p className="text-sm text-muted-foreground mb-4">
                  Create your first terminal to get started
                </p>
                <Button onClick={() => setShowAddDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Terminal
                </Button>
              </div>
            </div>
          )}

          {/* Fullscreen close button */}
          {isFullscreen && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-4 right-4"
              onClick={() => setIsFullscreen(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}