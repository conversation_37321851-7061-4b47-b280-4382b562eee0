#!/usr/bin/env tsx

/**
 * Workspace Templates <PERSON><PERSON>
 * 
 * Seeds the database with comprehensive workspace templates for different
 * development scenarios and technology stacks.
 * 
 * Usage:
 * pnpm seed:templates
 * or
 * npx tsx src/scripts/seed-workspace-templates.ts
 */

import { Client, Databases, ID } from 'node-appwrite';
import { config } from 'dotenv';
import { WorkspaceType } from '../types/workspace';

// Load environment variables
config({ path: '.env.local' });

interface TemplateFile {
  path: string;
  type: 'file' | 'directory';
  content?: string;
  mimeType?: string;
  language?: string;
}

interface WorkspaceTemplate {
  name: string;
  description: string;
  type: WorkspaceType;
  category: string;
  configuration: any;
  files: TemplateFile[];
  structure: any;
  version: string;
  author: string;
  authorId: string;
  visibility: 'public' | 'private' | 'team';
  tags: string[];
  requirements: any;
  usageCount: number;
  rating: number;
  featured: boolean;
  icon?: string;
  screenshots?: string[];
}

class WorkspaceTemplateSeeder {
  private client: Client;
  private databases: Databases;
  private config: {
    endpoint: string;
    projectId: string;
    apiKey: string;
    databaseId: string;
    templatesCollection: string;
  };

  constructor() {
    this.config = {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!,
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!,
      apiKey: process.env.APPWRITE_API_KEY!,
      databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!,
      templatesCollection: 'workspace-templates',
    };

    this.client = new Client()
      .setEndpoint(this.config.endpoint)
      .setProject(this.config.projectId)
      .setKey(this.config.apiKey);

    this.databases = new Databases(this.client);
  }

  async seed(): Promise<void> {
    console.log('🎨 Starting workspace template seeding...\n');

    try {
      const templates = this.getTemplateDefinitions();
      
      for (const template of templates) {
        await this.createTemplate(template);
      }

      console.log('\n✅ Workspace template seeding completed successfully!');

    } catch (error: any) {
      console.error('❌ Template seeding failed:', error.message);
      process.exit(1);
    }
  }

  private getTemplateDefinitions(): WorkspaceTemplate[] {
    return [
      {
        name: 'Python FastAPI Starter',
        description: 'Modern Python API development with FastAPI, SQLAlchemy, and async support',
        type: 'python',
        category: 'API Development',
        configuration: {
          runtime: {
            version: '3.11',
            environment: {
              PYTHONPATH: '/workspace',
              FASTAPI_ENV: 'development',
            },
            dependencies: [
              'fastapi[all]>=0.104.0',
              'sqlalchemy>=2.0.0',
              'alembic>=1.12.0',
              'pydantic>=2.0.0',
              'uvicorn[standard]>=0.24.0',
              'pytest>=7.4.0',
              'httpx>=0.25.0',
            ],
          },
          resources: { cpu: 2, memory: 2048, storage: 10 },
          editor: { theme: 'dark', fontSize: 14, tabSize: 4 },
          collaboration: { enabled: true, maxCollaborators: 5 },
        },
        files: [
          {
            path: '/main.py',
            type: 'file',
            content: `from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.v1.api import api_router
from app.core.config import settings

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# Set all CORS enabled origins
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

app.include_router(api_router, prefix=settings.API_V1_STR)

@app.get("/")
async def root():
    return {"message": "Welcome to FastAPI Starter"}
`,
            mimeType: 'text/x-python',
            language: 'python',
          },
          {
            path: '/requirements.txt',
            type: 'file',
            content: `fastapi[all]>=0.104.0
sqlalchemy>=2.0.0
alembic>=1.12.0
pydantic>=2.0.0
uvicorn[standard]>=0.24.0
pytest>=7.4.0
httpx>=0.25.0
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
`,
            mimeType: 'text/plain',
            language: 'text',
          },
          {
            path: '/app',
            type: 'directory',
          },
          {
            path: '/app/__init__.py',
            type: 'file',
            content: '',
            mimeType: 'text/x-python',
            language: 'python',
          },
          {
            path: '/app/core',
            type: 'directory',
          },
          {
            path: '/app/core/config.py',
            type: 'file',
            content: `import secrets
from typing import Any, Dict, List, Optional, Union
from pydantic import AnyHttpUrl, BaseSettings, EmailStr, validator

class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    PROJECT_NAME: str = "FastAPI Starter"
    
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    class Config:
        case_sensitive = True

settings = Settings()
`,
            mimeType: 'text/x-python',
            language: 'python',
          },
        ],
        structure: {
          type: 'directory',
          name: 'root',
          children: [
            { type: 'file', name: 'main.py' },
            { type: 'file', name: 'requirements.txt' },
            { type: 'file', name: 'README.md' },
            {
              type: 'directory',
              name: 'app',
              children: [
                { type: 'file', name: '__init__.py' },
                { type: 'directory', name: 'api', children: [] },
                { type: 'directory', name: 'core', children: [] },
                { type: 'directory', name: 'models', children: [] },
                { type: 'directory', name: 'schemas', children: [] },
              ],
            },
            { type: 'directory', name: 'tests', children: [] },
          ],
        },
        version: '1.0.0',
        author: 'Omnispace Team',
        authorId: 'system',
        visibility: 'public',
        tags: ['python', 'fastapi', 'api', 'async', 'sqlalchemy', 'starter'],
        requirements: {
          minCpu: 1,
          minMemory: 1024,
          minStorage: 5,
          dependencies: ['python>=3.9'],
        },
        usageCount: 156,
        rating: 4.9,
        featured: true,
        icon: '🚀',
        screenshots: ['/templates/fastapi-starter-1.png', '/templates/fastapi-starter-2.png'],
      },
      {
        name: 'React TypeScript SPA',
        description: 'Modern React single-page application with TypeScript, Vite, and Tailwind CSS',
        type: 'nodejs',
        category: 'Frontend Development',
        configuration: {
          runtime: {
            version: '18.17.0',
            environment: {
              NODE_ENV: 'development',
              VITE_API_URL: 'http://localhost:3000',
            },
            dependencies: [
              'react@^18.2.0',
              'react-dom@^18.2.0',
              'typescript@^5.0.0',
              'vite@^4.4.0',
              '@vitejs/plugin-react@^4.0.0',
              'tailwindcss@^3.3.0',
              'autoprefixer@^10.4.0',
              'postcss@^8.4.0',
            ],
          },
          resources: { cpu: 2, memory: 2048, storage: 15 },
          editor: { theme: 'dark', fontSize: 14, tabSize: 2 },
          collaboration: { enabled: true, maxCollaborators: 8 },
        },
        files: [
          {
            path: '/package.json',
            type: 'file',
            content: `{
  "name": "react-typescript-spa",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.15.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.3",
    "autoprefixer": "^10.4.14",
    "eslint": "^8.45.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.3",
    "postcss": "^8.4.27",
    "tailwindcss": "^3.3.3",
    "typescript": "^5.0.2",
    "vite": "^4.4.5"
  }
}`,
            mimeType: 'application/json',
            language: 'json',
          },
          {
            path: '/src/App.tsx',
            type: 'file',
            content: `import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Header } from './components/Header';
import { Home } from './pages/Home';
import { About } from './pages/About';
import './App.css';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;`,
            mimeType: 'text/typescript',
            language: 'typescript',
          },
        ],
        structure: {
          type: 'directory',
          name: 'root',
          children: [
            { type: 'file', name: 'package.json' },
            { type: 'file', name: 'vite.config.ts' },
            { type: 'file', name: 'tailwind.config.js' },
            { type: 'file', name: 'index.html' },
            {
              type: 'directory',
              name: 'src',
              children: [
                { type: 'file', name: 'App.tsx' },
                { type: 'file', name: 'main.tsx' },
                { type: 'directory', name: 'components', children: [] },
                { type: 'directory', name: 'pages', children: [] },
                { type: 'directory', name: 'hooks', children: [] },
                { type: 'directory', name: 'utils', children: [] },
              ],
            },
            { type: 'directory', name: 'public', children: [] },
          ],
        },
        version: '2.1.0',
        author: 'Omnispace Team',
        authorId: 'system',
        visibility: 'public',
        tags: ['react', 'typescript', 'vite', 'tailwind', 'spa', 'frontend'],
        requirements: {
          minCpu: 2,
          minMemory: 2048,
          minStorage: 10,
          dependencies: ['node>=16'],
        },
        usageCount: 234,
        rating: 4.8,
        featured: true,
        icon: '⚛️',
        screenshots: ['/templates/react-spa-1.png', '/templates/react-spa-2.png'],
      },
    ];
  }

  private async createTemplate(template: WorkspaceTemplate): Promise<void> {
    try {
      // Check if template already exists
      const existing = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.templatesCollection
      );

      const existingTemplate = existing.documents.find((doc: any) => doc.name === template.name);
      if (existingTemplate) {
        console.log(`ℹ️  Template '${template.name}' already exists`);
        return;
      }

      await this.databases.createDocument(
        this.config.databaseId,
        this.config.templatesCollection,
        ID.unique(),
        {
          ...template,
          configuration: JSON.stringify(template.configuration),
          files: JSON.stringify(template.files),
          structure: JSON.stringify(template.structure),
          requirements: JSON.stringify(template.requirements),
          screenshots: JSON.stringify(template.screenshots || []),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );

      console.log(`✅ Created template: ${template.name}`);

    } catch (error: any) {
      console.error(`❌ Failed to create template ${template.name}:`, error.message);
    }
  }
}

// Main execution
async function main() {
  const seeder = new WorkspaceTemplateSeeder();
  await seeder.seed();
}

// Run the seeder
if (require.main === module) {
  main().catch(console.error);
}
