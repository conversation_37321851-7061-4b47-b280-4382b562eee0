#!/usr/bin/env tsx

/**
 * Workspace Data Demo Script
 * 
 * Demonstrates the seeded workspace data by fetching and displaying
 * workspace information in a user-friendly format.
 * 
 * Usage:
 * pnpm demo:workspaces
 * or
 * npx tsx src/scripts/demo-workspace-data.ts
 */

import { Client, Databases } from 'node-appwrite';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

class WorkspaceDataDemo {
  private client: Client;
  private databases: Databases;
  private config: {
    endpoint: string;
    projectId: string;
    apiKey: string;
    databaseId: string;
    collections: {
      workspaces: string;
      templates: string;
      files: string;
      stats: string;
    };
  };

  constructor() {
    this.config = {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!,
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!,
      apiKey: process.env.APPWRITE_API_KEY!,
      databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!,
      collections: {
        workspaces: process.env.NEXT_PUBLIC_APPWRITE_WORKSPACES_COLLECTION_ID || 'workspaces',
        templates: 'workspace-templates',
        files: 'workspace-files',
        stats: 'workspace-stats',
      }
    };

    this.client = new Client()
      .setEndpoint(this.config.endpoint)
      .setProject(this.config.projectId)
      .setKey(this.config.apiKey);

    this.databases = new Databases(this.client);
  }

  async demo(): Promise<void> {
    console.log('🎭 Omnispace Workspace Data Demo\n');
    console.log('This demo showcases the seeded workspace data in your Omnispace platform.\n');

    try {
      await this.showWorkspaceTemplates();
      await this.showSampleWorkspaces();
      await this.showWorkspaceDetails();
      await this.showUsageStatistics();

      console.log('\n🎉 Demo completed! You can now explore these workspaces in your Omnispace platform.');
      console.log('💡 Navigate to /dashboard/workspaces to see the workspace interface.');

    } catch (error: any) {
      console.error('❌ Demo failed:', error.message);
      console.log('\n💡 Make sure you have run the seeding scripts first:');
      console.log('   pnpm seed:all');
    }
  }

  private async showWorkspaceTemplates(): Promise<void> {
    console.log('📋 Available Workspace Templates:\n');

    try {
      const result = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.templates
      );

      if (result.documents.length === 0) {
        console.log('   No templates found. Run: pnpm seed:templates\n');
        return;
      }

      result.documents.forEach((template, index) => {
        const config = JSON.parse(template.configuration || '{}');
        console.log(`   ${index + 1}. ${template.name} ${template.icon || '📦'}`);
        console.log(`      Type: ${template.type} | Category: ${template.category}`);
        console.log(`      Rating: ${'⭐'.repeat(Math.floor(template.rating))} (${template.rating}/5)`);
        console.log(`      Usage: ${template.usageCount} times | Featured: ${template.featured ? 'Yes' : 'No'}`);
        console.log(`      Resources: ${config.resources?.cpu || 'N/A'} CPU, ${config.resources?.memory || 'N/A'}MB RAM`);
        console.log(`      Tags: ${template.tags?.join(', ') || 'None'}`);
        console.log();
      });

    } catch (error: any) {
      console.log(`   Error loading templates: ${error.message}\n`);
    }
  }

  private async showSampleWorkspaces(): Promise<void> {
    console.log('💻 Sample Workspaces:\n');

    try {
      const result = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.workspaces
      );

      if (result.documents.length === 0) {
        console.log('   No workspaces found. Run: pnpm seed:workspaces\n');
        return;
      }

      result.documents.forEach((workspace, index) => {
        const statusIcon = this.getStatusIcon(workspace.status);
        const visibilityIcon = this.getVisibilityIcon(workspace.visibility);
        const sizeFormatted = this.formatBytes(workspace.totalSize || 0);

        console.log(`   ${index + 1}. ${workspace.name} ${statusIcon}`);
        console.log(`      Type: ${workspace.type} | Status: ${workspace.status} | ${visibilityIcon} ${workspace.visibility}`);
        console.log(`      Owner: ${workspace.ownerName}`);
        console.log(`      Files: ${workspace.fileCount || 0} | Size: ${sizeFormatted}`);
        console.log(`      AI Enabled: ${workspace.aiEnabled ? 'Yes' : 'No'}`);
        console.log(`      Tags: ${workspace.tags?.join(', ') || 'None'}`);
        console.log(`      Created: ${new Date(workspace.createdAt).toLocaleDateString()}`);
        console.log();
      });

    } catch (error: any) {
      console.log(`   Error loading workspaces: ${error.message}\n`);
    }
  }

  private async showWorkspaceDetails(): Promise<void> {
    console.log('🔍 Detailed Workspace Example:\n');

    try {
      const result = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.workspaces,
        undefined,
        1
      );

      if (result.documents.length === 0) {
        console.log('   No workspaces available for detailed view.\n');
        return;
      }

      const workspace = result.documents[0];
      const config = JSON.parse(workspace.configuration || '{}');
      const aiConfig = JSON.parse(workspace.aiAssistantConfig || '{}');

      console.log(`   📦 ${workspace.name}`);
      console.log(`   ${workspace.description}`);
      console.log();
      console.log(`   🔧 Configuration:`);
      console.log(`      Runtime: ${config.runtime?.version || 'N/A'}`);
      console.log(`      CPU: ${config.resources?.cpu || 'N/A'} cores`);
      console.log(`      Memory: ${config.resources?.memory || 'N/A'} MB`);
      console.log(`      Storage: ${config.resources?.storage || 'N/A'} GB`);
      console.log();
      console.log(`   🤖 AI Assistant:`);
      console.log(`      Model: ${aiConfig.model || 'N/A'}`);
      console.log(`      Features: ${Object.keys(aiConfig.features || {}).filter(k => aiConfig.features[k]).join(', ') || 'None'}`);
      console.log(`      Context Window: ${aiConfig.contextWindow || 'N/A'} tokens`);
      console.log();

      // Show files for this workspace
      try {
        const filesResult = await this.databases.listDocuments(
          this.config.databaseId,
          this.config.collections.files,
          [`workspaceId=${workspace.$id}`],
          5
        );

        if (filesResult.documents.length > 0) {
          console.log(`   📁 Sample Files (${filesResult.total} total):`);
          filesResult.documents.forEach(file => {
            const typeIcon = file.type === 'directory' ? '📁' : '📄';
            const sizeStr = file.size ? ` (${this.formatBytes(file.size)})` : '';
            console.log(`      ${typeIcon} ${file.path}${sizeStr}`);
          });
          console.log();
        }
      } catch (error) {
        // Files might not be seeded, that's okay
      }

    } catch (error: any) {
      console.log(`   Error loading workspace details: ${error.message}\n`);
    }
  }

  private async showUsageStatistics(): Promise<void> {
    console.log('📊 Usage Statistics Summary:\n');

    try {
      const workspacesResult = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.workspaces
      );

      const statsResult = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.stats
      );

      // Workspace statistics
      const statusCounts = workspacesResult.documents.reduce((acc, doc) => {
        acc[doc.status] = (acc[doc.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const typeCounts = workspacesResult.documents.reduce((acc, doc) => {
        acc[doc.type] = (acc[doc.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      console.log(`   📈 Workspace Overview:`);
      console.log(`      Total Workspaces: ${workspacesResult.total}`);
      console.log(`      By Status: ${Object.entries(statusCounts).map(([k,v]) => `${k}: ${v}`).join(', ')}`);
      console.log(`      By Type: ${Object.entries(typeCounts).map(([k,v]) => `${k}: ${v}`).join(', ')}`);
      console.log();

      // Usage statistics
      if (statsResult.documents.length > 0) {
        const totalUptime = statsResult.documents.reduce((sum, stat) => sum + (stat.totalUptime || 0), 0);
        const totalSessions = statsResult.documents.reduce((sum, stat) => sum + (stat.totalSessions || 0), 0);
        const avgCpuUsage = statsResult.documents.reduce((sum, stat) => sum + (stat.averageCpuUsage || 0), 0) / statsResult.documents.length;

        console.log(`   📊 Usage Statistics:`);
        console.log(`      Total Uptime: ${this.formatDuration(totalUptime)}`);
        console.log(`      Total Sessions: ${totalSessions}`);
        console.log(`      Average CPU Usage: ${avgCpuUsage.toFixed(1)}%`);
        console.log();
      }

    } catch (error: any) {
      console.log(`   Error loading statistics: ${error.message}\n`);
    }
  }

  private getStatusIcon(status: string): string {
    const icons: Record<string, string> = {
      'active': '🟢',
      'stopped': '🔴',
      'creating': '🟡',
      'error': '❌',
      'archived': '📦',
    };
    return icons[status] || '⚪';
  }

  private getVisibilityIcon(visibility: string): string {
    const icons: Record<string, string> = {
      'private': '🔒',
      'team': '👥',
      'public': '🌐',
    };
    return icons[visibility] || '🔒';
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  private formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }
}

// Main execution
async function main() {
  const demo = new WorkspaceDataDemo();
  await demo.demo();
}

// Run the demo
if (require.main === module) {
  main().catch(console.error);
}
