#!/usr/bin/env tsx

/**
 * Comprehensive Workspace Seeder Script
 * 
 * Seeds the database with realistic workspace data including:
 * - Various workspace types (Python, Node.js, General, Collaborative)
 * - Different statuses and configurations
 * - Sample files and directory structures
 * - Workspace templates
 * - Collaboration data
 * 
 * Usage:
 * pnpm seed:workspaces
 * or
 * npx tsx src/scripts/seed-workspaces.ts
 */

import { Client, Databases, ID } from 'node-appwrite';
import { config } from 'dotenv';
import { WorkspaceType, WorkspaceStatus, WorkspaceVisibility } from '../types/workspace';

// Load environment variables
config({ path: '.env.local' });

interface SeedWorkspace {
  name: string;
  description: string;
  type: WorkspaceType;
  status: WorkspaceStatus;
  visibility: WorkspaceVisibility;
  ownerId: string;
  ownerName: string;
  configuration: any;
  templateId?: string;
  tags: string[];
  category: string;
  aiEnabled: boolean;
  aiAssistantConfig: any;
  rootPath: string;
  fileCount: number;
  totalSize: number;
}

interface SeedTemplate {
  name: string;
  description: string;
  type: WorkspaceType;
  category: string;
  configuration: any;
  files: any[];
  structure: any;
  version: string;
  author: string;
  authorId: string;
  visibility: 'public' | 'private' | 'team';
  tags: string[];
  requirements: any;
  usageCount: number;
  rating: number;
}

class WorkspaceSeeder {
  private client: Client;
  private databases: Databases;
  private config: {
    endpoint: string;
    projectId: string;
    apiKey: string;
    databaseId: string;
    collections: {
      workspaces: string;
      templates: string;
      files: string;
      permissions: string;
      stats: string;
    };
  };

  constructor() {
    this.config = {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!,
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!,
      apiKey: process.env.APPWRITE_API_KEY!,
      databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!,
      collections: {
        workspaces: process.env.NEXT_PUBLIC_APPWRITE_WORKSPACES_COLLECTION_ID || 'workspaces',
        templates: 'workspace-templates',
        files: 'workspace-files',
        permissions: 'workspace-permissions',
        stats: 'workspace-stats',
      }
    };

    // Initialize Appwrite client
    this.client = new Client()
      .setEndpoint(this.config.endpoint)
      .setProject(this.config.projectId)
      .setKey(this.config.apiKey);

    this.databases = new Databases(this.client);
  }

  async seed(): Promise<void> {
    console.log('🌱 Starting workspace seeding...\n');

    try {
      // Get existing users for workspace ownership
      const users = await this.getExistingUsers();
      if (users.length === 0) {
        console.log('❌ No users found. Please run user seeding first.');
        return;
      }

      // Seed workspace templates first
      await this.seedWorkspaceTemplates();

      // Seed workspaces
      await this.seedWorkspaces(users);

      // Seed workspace files
      await this.seedWorkspaceFiles();

      // Seed workspace stats
      await this.seedWorkspaceStats();

      console.log('\n✅ Workspace seeding completed successfully!');

    } catch (error: any) {
      console.error('❌ Seeding failed:', error.message);
      process.exit(1);
    }
  }

  private async getExistingUsers(): Promise<any[]> {
    try {
      const usersCollection = process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID || 'users';
      const result = await this.databases.listDocuments(
        this.config.databaseId,
        usersCollection
      );
      return result.documents;
    } catch (error) {
      console.log('ℹ️  No existing users found or users collection not accessible');
      return [];
    }
  }

  private async seedWorkspaceTemplates(): Promise<void> {
    console.log('📋 Seeding workspace templates...');

    const templates: SeedTemplate[] = [
      {
        name: 'Python Django Starter',
        description: 'A complete Django web application template with authentication, admin panel, and REST API setup',
        type: 'python',
        category: 'Web Development',
        configuration: {
          runtime: {
            version: '3.11',
            environment: {
              DJANGO_SETTINGS_MODULE: 'myproject.settings',
              DEBUG: 'True',
            },
            dependencies: ['django>=4.2', 'djangorestframework', 'django-cors-headers', 'celery', 'redis'],
          },
          resources: {
            cpu: 2,
            memory: 2048,
            storage: 10,
          },
          editor: {
            theme: 'dark',
            fontSize: 14,
            tabSize: 4,
          },
        },
        files: [
          { path: 'manage.py', type: 'file', content: '#!/usr/bin/env python\nimport os\nimport sys...' },
          { path: 'requirements.txt', type: 'file', content: 'django>=4.2\ndjangorestframework\n...' },
          { path: 'myproject/', type: 'directory' },
          { path: 'myproject/settings.py', type: 'file', content: 'Django settings...' },
        ],
        structure: {
          type: 'directory',
          name: 'root',
          children: [
            { type: 'file', name: 'manage.py' },
            { type: 'file', name: 'requirements.txt' },
            { type: 'directory', name: 'myproject', children: [] },
            { type: 'directory', name: 'apps', children: [] },
          ],
        },
        version: '1.0.0',
        author: 'Omnispace Team',
        authorId: 'system',
        visibility: 'public',
        tags: ['python', 'django', 'web', 'api', 'starter'],
        requirements: {
          minCpu: 1,
          minMemory: 1024,
          minStorage: 5,
          dependencies: ['python>=3.9'],
        },
        usageCount: 45,
        rating: 4.8,
      },
      {
        name: 'Next.js TypeScript App',
        description: 'Modern Next.js application with TypeScript, Tailwind CSS, and authentication',
        type: 'nodejs',
        category: 'Web Development',
        configuration: {
          runtime: {
            version: '18.17.0',
            environment: {
              NODE_ENV: 'development',
              NEXT_TELEMETRY_DISABLED: '1',
            },
            dependencies: ['next', 'react', 'react-dom', 'typescript', '@types/node', '@types/react'],
          },
          resources: {
            cpu: 2,
            memory: 3072,
            storage: 15,
          },
        },
        files: [
          { path: 'package.json', type: 'file', content: '{"name": "nextjs-app"...}' },
          { path: 'next.config.js', type: 'file', content: 'module.exports = {...}' },
          { path: 'src/', type: 'directory' },
        ],
        structure: {
          type: 'directory',
          name: 'root',
          children: [
            { type: 'file', name: 'package.json' },
            { type: 'file', name: 'next.config.js' },
            { type: 'directory', name: 'src', children: [] },
            { type: 'directory', name: 'public', children: [] },
          ],
        },
        version: '1.2.0',
        author: 'Omnispace Team',
        authorId: 'system',
        visibility: 'public',
        tags: ['nodejs', 'nextjs', 'typescript', 'react', 'tailwind'],
        requirements: {
          minCpu: 2,
          minMemory: 2048,
          minStorage: 10,
          dependencies: ['node>=16'],
        },
        usageCount: 78,
        rating: 4.9,
      },
      {
        name: 'Machine Learning Workspace',
        description: 'Complete ML environment with Jupyter, TensorFlow, PyTorch, and data science tools',
        type: 'python',
        category: 'Data Science',
        configuration: {
          runtime: {
            version: '3.10',
            environment: {
              JUPYTER_ENABLE_LAB: 'yes',
              CUDA_VISIBLE_DEVICES: '0',
            },
            dependencies: ['jupyter', 'tensorflow', 'torch', 'numpy', 'pandas', 'scikit-learn', 'matplotlib'],
          },
          resources: {
            cpu: 4,
            memory: 8192,
            storage: 50,
          },
        },
        files: [
          { path: 'notebooks/', type: 'directory' },
          { path: 'data/', type: 'directory' },
          { path: 'models/', type: 'directory' },
          { path: 'requirements.txt', type: 'file', content: 'jupyter\ntensorflow\n...' },
        ],
        structure: {
          type: 'directory',
          name: 'root',
          children: [
            { type: 'directory', name: 'notebooks', children: [] },
            { type: 'directory', name: 'data', children: [] },
            { type: 'directory', name: 'models', children: [] },
            { type: 'file', name: 'requirements.txt' },
          ],
        },
        version: '2.1.0',
        author: 'Omnispace Team',
        authorId: 'system',
        visibility: 'public',
        tags: ['python', 'ml', 'jupyter', 'tensorflow', 'pytorch', 'data-science'],
        requirements: {
          minCpu: 2,
          minMemory: 4096,
          minStorage: 20,
          dependencies: ['python>=3.8'],
        },
        usageCount: 32,
        rating: 4.7,
      },
    ];

    for (const template of templates) {
      try {
        // Check if template already exists
        const existing = await this.databases.listDocuments(
          this.config.databaseId,
          this.config.collections.templates
        );

        const existingTemplate = existing.documents.find((doc: any) => doc.name === template.name);
        if (existingTemplate) {
          console.log(`ℹ️  Template '${template.name}' already exists`);
          continue;
        }

        await this.databases.createDocument(
          this.config.databaseId,
          this.config.collections.templates,
          ID.unique(),
          {
            ...template,
            configuration: JSON.stringify(template.configuration),
            files: JSON.stringify(template.files),
            structure: JSON.stringify(template.structure),
            requirements: JSON.stringify(template.requirements),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        );

        console.log(`✅ Created template: ${template.name}`);

      } catch (error: any) {
        console.error(`❌ Failed to create template ${template.name}:`, error.message);
      }
    }
  }

  private async seedWorkspaces(users: any[]): Promise<void> {
    console.log('💻 Seeding workspaces...');

    const workspaces: SeedWorkspace[] = [
      {
        name: 'E-commerce Platform Development',
        description: 'Building a scalable e-commerce platform with Django REST API and React frontend',
        type: 'python',
        status: 'active',
        visibility: 'private',
        ownerId: users[0].$id,
        ownerName: users[0].name || users[0].email.split('@')[0],
        configuration: {
          runtime: {
            version: '3.11',
            environment: {
              DJANGO_SETTINGS_MODULE: 'ecommerce.settings.development',
              DEBUG: 'True',
              REDIS_URL: 'redis://localhost:6379',
            },
            dependencies: ['django>=4.2', 'djangorestframework', 'celery', 'redis', 'stripe'],
          },
          resources: {
            cpu: 4,
            memory: 4096,
            storage: 25,
          },
          editor: {
            theme: 'dark',
            fontSize: 14,
            tabSize: 4,
          },
          collaboration: {
            enabled: true,
            maxCollaborators: 5,
          },
        },
        templateId: null,
        tags: ['python', 'django', 'ecommerce', 'api', 'production'],
        category: 'Web Development',
        aiEnabled: true,
        aiAssistantConfig: {
          enabled: true,
          model: 'gpt-4',
          features: {
            codeCompletion: true,
            codeAnalysis: true,
            errorDetection: true,
            refactoring: true,
            documentation: true,
            testing: true,
            debugging: true,
            terminalAssistance: true,
          },
          contextWindow: 8000,
          temperature: 0.7,
        },
        rootPath: '/workspaces/ecommerce-platform',
        fileCount: 127,
        totalSize: 15680000, // ~15MB
      },
      {
        name: 'Next.js Video Conferencing System',
        description: 'Building a production-ready NextJs Video Conferencing System for Teams',
        type: 'nodejs',
        status: 'active',
        visibility: 'team',
        ownerId: users[Math.min(1, users.length - 1)].$id,
        ownerName: users[Math.min(1, users.length - 1)].name || users[Math.min(1, users.length - 1)].email.split('@')[0],
        configuration: {
          runtime: {
            version: '18.17.0',
            environment: {
              NODE_ENV: 'development',
              NEXT_TELEMETRY_DISABLED: '1',
              NEXTAUTH_SECRET: 'dev-secret',
              WEBRTC_STUN_SERVER: 'stun:stun.l.google.com:19302',
            },
            dependencies: ['next', 'react', 'typescript', 'next-auth', 'socket.io', 'webrtc'],
          },
          resources: {
            cpu: 3,
            memory: 3072,
            storage: 20,
          },
          editor: {
            theme: 'dark',
            fontSize: 13,
            tabSize: 2,
          },
          collaboration: {
            enabled: true,
            maxCollaborators: 8,
          },
        },
        templateId: null,
        tags: ['nextjs', 'typescript', 'webrtc', 'video', 'conferencing', 'realtime'],
        category: 'Web Development',
        aiEnabled: true,
        aiAssistantConfig: {
          enabled: true,
          model: 'gpt-4',
          features: {
            codeCompletion: true,
            codeAnalysis: true,
            errorDetection: true,
            refactoring: true,
            documentation: true,
            testing: true,
            debugging: true,
            terminalAssistance: true,
          },
          contextWindow: 8000,
          temperature: 0.7,
        },
        rootPath: '/workspaces/video-conferencing-system',
        fileCount: 89,
        totalSize: 12340000, // ~12MB
      },
      {
        name: 'Machine Learning Research Project',
        description: 'Deep learning research on computer vision with PyTorch and CUDA acceleration',
        type: 'python',
        status: 'active',
        visibility: 'public',
        ownerId: users[Math.min(2, users.length - 1)].$id,
        ownerName: users[Math.min(2, users.length - 1)].name || users[Math.min(2, users.length - 1)].email.split('@')[0],
        configuration: {
          runtime: {
            version: '3.10',
            environment: {
              CUDA_VISIBLE_DEVICES: '0',
              PYTORCH_CUDA_ALLOC_CONF: 'max_split_size_mb:512',
              JUPYTER_ENABLE_LAB: 'yes',
            },
            dependencies: ['torch', 'torchvision', 'jupyter', 'numpy', 'matplotlib', 'opencv-python'],
          },
          resources: {
            cpu: 6,
            memory: 16384,
            storage: 100,
          },
          editor: {
            theme: 'light',
            fontSize: 12,
            tabSize: 4,
          },
          collaboration: {
            enabled: true,
            maxCollaborators: 3,
          },
        },
        templateId: null,
        tags: ['python', 'pytorch', 'ml', 'research', 'computer-vision', 'cuda'],
        category: 'Data Science',
        aiEnabled: true,
        aiAssistantConfig: {
          enabled: true,
          model: 'gpt-4',
          features: {
            codeCompletion: true,
            codeAnalysis: true,
            errorDetection: true,
            refactoring: true,
            documentation: true,
            testing: false,
            debugging: true,
            terminalAssistance: true,
          },
          contextWindow: 12000,
          temperature: 0.5,
        },
        rootPath: '/workspaces/ml-research',
        fileCount: 234,
        totalSize: 45670000, // ~45MB
      },
      {
        name: 'React Native Mobile App',
        description: 'Cross-platform mobile application with React Native and Expo',
        type: 'nodejs',
        status: 'stopped',
        visibility: 'private',
        ownerId: users[0].$id,
        ownerName: users[0].name || users[0].email.split('@')[0],
        configuration: {
          runtime: {
            version: '18.17.0',
            environment: {
              EXPO_CLI_VERSION: '6.0.0',
              REACT_NATIVE_VERSION: '0.72.0',
            },
            dependencies: ['expo', 'react-native', 'react-navigation', '@expo/vector-icons'],
          },
          resources: {
            cpu: 2,
            memory: 2048,
            storage: 15,
          },
          editor: {
            theme: 'dark',
            fontSize: 14,
            tabSize: 2,
          },
          collaboration: {
            enabled: false,
            maxCollaborators: 1,
          },
        },
        templateId: null,
        tags: ['react-native', 'mobile', 'expo', 'cross-platform'],
        category: 'Mobile Development',
        aiEnabled: true,
        aiAssistantConfig: {
          enabled: true,
          model: 'gpt-3.5-turbo',
          features: {
            codeCompletion: true,
            codeAnalysis: true,
            errorDetection: true,
            refactoring: false,
            documentation: true,
            testing: false,
            debugging: true,
            terminalAssistance: true,
          },
          contextWindow: 4000,
          temperature: 0.8,
        },
        rootPath: '/workspaces/mobile-app',
        fileCount: 67,
        totalSize: 8920000, // ~9MB
      },
      {
        name: 'DevOps Infrastructure Setup',
        description: 'Infrastructure as Code with Terraform, Docker, and Kubernetes configurations',
        type: 'general',
        status: 'creating',
        visibility: 'team',
        ownerId: users[Math.min(1, users.length - 1)].$id,
        ownerName: users[Math.min(1, users.length - 1)].name || users[Math.min(1, users.length - 1)].email.split('@')[0],
        configuration: {
          runtime: {
            version: 'latest',
            environment: {
              TERRAFORM_VERSION: '1.5.0',
              KUBECTL_VERSION: '1.27.0',
              DOCKER_BUILDKIT: '1',
            },
            dependencies: ['terraform', 'kubectl', 'docker', 'helm'],
          },
          resources: {
            cpu: 2,
            memory: 2048,
            storage: 30,
          },
          editor: {
            theme: 'dark',
            fontSize: 13,
            tabSize: 2,
          },
          collaboration: {
            enabled: true,
            maxCollaborators: 4,
          },
        },
        templateId: null,
        tags: ['devops', 'terraform', 'kubernetes', 'docker', 'infrastructure'],
        category: 'DevOps',
        aiEnabled: true,
        aiAssistantConfig: {
          enabled: true,
          model: 'gpt-4',
          features: {
            codeCompletion: true,
            codeAnalysis: true,
            errorDetection: true,
            refactoring: true,
            documentation: true,
            testing: false,
            debugging: true,
            terminalAssistance: true,
          },
          contextWindow: 6000,
          temperature: 0.6,
        },
        rootPath: '/workspaces/devops-infra',
        fileCount: 45,
        totalSize: 3450000, // ~3.5MB
      },
    ];

    for (const workspace of workspaces) {
      try {
        // Check if workspace already exists
        const existing = await this.databases.listDocuments(
          this.config.databaseId,
          this.config.collections.workspaces
        );

        const existingWorkspace = existing.documents.find((doc: any) => doc.name === workspace.name);
        if (existingWorkspace) {
          console.log(`ℹ️  Workspace '${workspace.name}' already exists`);
          continue;
        }

        await this.databases.createDocument(
          this.config.databaseId,
          this.config.collections.workspaces,
          ID.unique(),
          {
            ...workspace,
            configuration: JSON.stringify(workspace.configuration),
            aiAssistantConfig: JSON.stringify(workspace.aiAssistantConfig),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            lastAccessedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(), // Random within last week
          }
        );

        console.log(`✅ Created workspace: ${workspace.name}`);

      } catch (error: any) {
        console.error(`❌ Failed to create workspace ${workspace.name}:`, error.message);
      }
    }
  }

  private async seedWorkspaceFiles(): Promise<void> {
    console.log('📁 Seeding workspace files...');

    // Get existing workspaces
    const workspaces = await this.databases.listDocuments(
      this.config.databaseId,
      this.config.collections.workspaces
    );

    const sampleFiles = [
      {
        name: 'README.md',
        path: '/README.md',
        type: 'file',
        content: '# Project Documentation\n\nThis is a sample project created in Omnispace.\n\n## Getting Started\n\n1. Install dependencies\n2. Run the development server\n3. Start coding!\n',
        mimeType: 'text/markdown',
        size: 150,
        language: 'markdown',
      },
      {
        name: 'src',
        path: '/src',
        type: 'directory',
        content: null,
        mimeType: null,
        size: 0,
        language: null,
      },
      {
        name: 'main.py',
        path: '/src/main.py',
        type: 'file',
        content: '#!/usr/bin/env python3\n"""\nMain application entry point\n"""\n\ndef main():\n    print("Hello, Omnispace!")\n    return 0\n\nif __name__ == "__main__":\n    main()\n',
        mimeType: 'text/x-python',
        size: 180,
        language: 'python',
      },
      {
        name: 'package.json',
        path: '/package.json',
        type: 'file',
        content: '{\n  "name": "omnispace-project",\n  "version": "1.0.0",\n  "description": "A project created in Omnispace",\n  "main": "index.js",\n  "scripts": {\n    "start": "node index.js",\n    "dev": "nodemon index.js"\n  },\n  "dependencies": {},\n  "devDependencies": {}\n}\n',
        mimeType: 'application/json',
        size: 280,
        language: 'json',
      },
      {
        name: '.gitignore',
        path: '/.gitignore',
        type: 'file',
        content: 'node_modules/\n*.log\n.env\n.env.local\n.DS_Store\n__pycache__/\n*.pyc\n.pytest_cache/\ndist/\nbuild/\n',
        mimeType: 'text/plain',
        size: 95,
        language: 'gitignore',
      },
    ];

    for (const workspace of workspaces.documents.slice(0, 3)) { // Only seed files for first 3 workspaces
      for (const file of sampleFiles) {
        try {
          // Check if file already exists
          const existing = await this.databases.listDocuments(
            this.config.databaseId,
            this.config.collections.files,
            [`workspaceId=${workspace.$id}`, `path=${file.path}`]
          );

          if (existing.documents.length > 0) {
            continue; // File already exists
          }

          await this.databases.createDocument(
            this.config.databaseId,
            this.config.collections.files,
            ID.unique(),
            {
              workspaceId: workspace.$id,
              name: file.name,
              path: file.path,
              type: file.type,
              content: file.content,
              mimeType: file.mimeType,
              size: file.size,
              language: file.language,
              encoding: 'utf-8',
              version: 1,
              isLocked: false,
              isOpen: false,
              isDirty: false,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              createdBy: workspace.ownerId,
              updatedBy: workspace.ownerId,
            }
          );

        } catch (error: any) {
          console.error(`❌ Failed to create file ${file.name} for workspace ${workspace.name}:`, error.message);
        }
      }
      console.log(`✅ Created sample files for workspace: ${workspace.name}`);
    }
  }

  private async seedWorkspaceStats(): Promise<void> {
    console.log('📊 Seeding workspace statistics...');

    // Get existing workspaces
    const workspaces = await this.databases.listDocuments(
      this.config.databaseId,
      this.config.collections.workspaces
    );

    for (const workspace of workspaces.documents) {
      try {
        // Check if stats already exist
        const existing = await this.databases.listDocuments(
          this.config.databaseId,
          this.config.collections.stats,
          [`workspaceId=${workspace.$id}`]
        );

        if (existing.documents.length > 0) {
          console.log(`ℹ️  Stats for workspace '${workspace.name}' already exist`);
          continue;
        }

        // Generate realistic stats based on workspace status
        const isActive = workspace.status === 'active';
        const baseUptime = isActive ? Math.random() * 7 * 24 * 60 * 60 * 1000 : 0; // Up to 7 days for active workspaces

        await this.databases.createDocument(
          this.config.databaseId,
          this.config.collections.stats,
          ID.unique(),
          {
            workspaceId: workspace.$id,
            totalUptime: Math.floor(baseUptime / 1000), // in seconds
            totalSessions: Math.floor(Math.random() * 50) + 1,
            totalCommands: Math.floor(Math.random() * 500) + 10,
            totalFilesCreated: Math.floor(Math.random() * 100) + 5,
            totalFilesModified: Math.floor(Math.random() * 200) + 10,
            totalLinesOfCode: Math.floor(Math.random() * 10000) + 100,
            averageSessionDuration: Math.floor(Math.random() * 3600) + 300, // 5 minutes to 1 hour
            lastActivityAt: isActive
              ? new Date(Date.now() - Math.random() * 60 * 60 * 1000).toISOString() // Within last hour for active
              : new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(), // Within last week for others
            peakCpuUsage: Math.floor(Math.random() * 80) + 10, // 10-90%
            peakMemoryUsage: Math.floor(Math.random() * 80) + 10, // 10-90%
            averageCpuUsage: Math.floor(Math.random() * 40) + 5, // 5-45%
            averageMemoryUsage: Math.floor(Math.random() * 50) + 10, // 10-60%
            networkBytesIn: Math.floor(Math.random() * 1000000000), // Up to 1GB
            networkBytesOut: Math.floor(Math.random() * 500000000), // Up to 500MB
            storageUsed: workspace.totalSize || Math.floor(Math.random() * 50000000), // Use workspace size or random
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        );

        console.log(`✅ Created stats for workspace: ${workspace.name}`);

      } catch (error: any) {
        console.error(`❌ Failed to create stats for workspace ${workspace.name}:`, error.message);
      }
    }
  }
}

// Main execution
async function main() {
  const seeder = new WorkspaceSeeder();
  await seeder.seed();
}

// Run the seeder
if (require.main === module) {
  main().catch(console.error);
}
