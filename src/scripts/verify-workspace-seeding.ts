#!/usr/bin/env tsx

/**
 * Workspace Seeding Verification Script
 * 
 * Verifies that workspace seeding was successful and provides
 * a summary of the seeded data.
 * 
 * Usage:
 * pnpm verify:workspaces
 * or
 * npx tsx src/scripts/verify-workspace-seeding.ts
 */

import { Client, Databases } from 'node-appwrite';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

interface VerificationResult {
  collection: string;
  count: number;
  status: 'success' | 'warning' | 'error';
  message: string;
  samples?: any[];
}

class WorkspaceSeederVerifier {
  private client: Client;
  private databases: Databases;
  private config: {
    endpoint: string;
    projectId: string;
    apiKey: string;
    databaseId: string;
    collections: {
      workspaces: string;
      templates: string;
      files: string;
      stats: string;
      users: string;
    };
  };

  constructor() {
    this.config = {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!,
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!,
      apiKey: process.env.APPWRITE_API_KEY!,
      databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!,
      collections: {
        workspaces: process.env.NEXT_PUBLIC_APPWRITE_WORKSPACES_COLLECTION_ID || 'workspaces',
        templates: 'workspace-templates',
        files: 'workspace-files',
        stats: 'workspace-stats',
        users: process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID || 'users',
      }
    };

    this.client = new Client()
      .setEndpoint(this.config.endpoint)
      .setProject(this.config.projectId)
      .setKey(this.config.apiKey);

    this.databases = new Databases(this.client);
  }

  async verify(): Promise<void> {
    console.log('🔍 Verifying workspace seeding...\n');

    try {
      const results: VerificationResult[] = [];

      // Verify users (prerequisite)
      results.push(await this.verifyUsers());

      // Verify workspace templates
      results.push(await this.verifyTemplates());

      // Verify workspaces
      results.push(await this.verifyWorkspaces());

      // Verify workspace files
      results.push(await this.verifyWorkspaceFiles());

      // Verify workspace stats
      results.push(await this.verifyWorkspaceStats());

      // Display results
      this.displayResults(results);

      // Overall status
      const hasErrors = results.some(r => r.status === 'error');
      const hasWarnings = results.some(r => r.status === 'warning');

      if (hasErrors) {
        console.log('\n❌ Verification failed with errors. Please check the issues above.');
        process.exit(1);
      } else if (hasWarnings) {
        console.log('\n⚠️  Verification completed with warnings. Some data may be missing.');
      } else {
        console.log('\n✅ All workspace seeding verification passed successfully!');
      }

    } catch (error: any) {
      console.error('❌ Verification failed:', error.message);
      process.exit(1);
    }
  }

  private async verifyUsers(): Promise<VerificationResult> {
    try {
      const result = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.users,
        undefined,
        5 // Limit to 5 for samples
      );

      if (result.documents.length === 0) {
        return {
          collection: 'Users',
          count: 0,
          status: 'error',
          message: 'No users found. Workspaces require existing users as owners.',
        };
      }

      return {
        collection: 'Users',
        count: result.total,
        status: 'success',
        message: `Found ${result.total} users`,
        samples: result.documents.slice(0, 3).map(doc => ({
          id: doc.$id,
          email: doc.email,
          name: doc.name,
        })),
      };

    } catch (error: any) {
      return {
        collection: 'Users',
        count: 0,
        status: 'error',
        message: `Failed to verify users: ${error.message}`,
      };
    }
  }

  private async verifyTemplates(): Promise<VerificationResult> {
    try {
      const result = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.templates,
        undefined,
        10
      );

      const expectedTemplates = [
        'Python FastAPI Starter',
        'React TypeScript SPA',
        'Python Django Starter',
        'Next.js TypeScript App',
        'Machine Learning Workspace',
      ];

      const foundTemplates = result.documents.map(doc => doc.name);
      const missingTemplates = expectedTemplates.filter(name => !foundTemplates.includes(name));

      let status: 'success' | 'warning' | 'error' = 'success';
      let message = `Found ${result.total} templates`;

      if (result.total === 0) {
        status = 'error';
        message = 'No templates found. Run pnpm seed:templates first.';
      } else if (missingTemplates.length > 0) {
        status = 'warning';
        message = `Found ${result.total} templates, but missing: ${missingTemplates.join(', ')}`;
      }

      return {
        collection: 'Workspace Templates',
        count: result.total,
        status,
        message,
        samples: result.documents.slice(0, 3).map(doc => ({
          name: doc.name,
          type: doc.type,
          category: doc.category,
          featured: doc.featured,
          usageCount: doc.usageCount,
        })),
      };

    } catch (error: any) {
      return {
        collection: 'Workspace Templates',
        count: 0,
        status: 'error',
        message: `Failed to verify templates: ${error.message}`,
      };
    }
  }

  private async verifyWorkspaces(): Promise<VerificationResult> {
    try {
      const result = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.workspaces,
        undefined,
        10
      );

      if (result.total === 0) {
        return {
          collection: 'Workspaces',
          count: 0,
          status: 'warning',
          message: 'No workspaces found. Run pnpm seed:workspaces to create sample workspaces.',
        };
      }

      // Analyze workspace distribution
      const statusCounts = result.documents.reduce((acc, doc) => {
        acc[doc.status] = (acc[doc.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const typeCounts = result.documents.reduce((acc, doc) => {
        acc[doc.type] = (acc[doc.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        collection: 'Workspaces',
        count: result.total,
        status: 'success',
        message: `Found ${result.total} workspaces (Status: ${Object.entries(statusCounts).map(([k,v]) => `${k}:${v}`).join(', ')})`,
        samples: result.documents.slice(0, 3).map(doc => ({
          name: doc.name,
          type: doc.type,
          status: doc.status,
          visibility: doc.visibility,
          fileCount: doc.fileCount,
          totalSize: doc.totalSize,
        })),
      };

    } catch (error: any) {
      return {
        collection: 'Workspaces',
        count: 0,
        status: 'error',
        message: `Failed to verify workspaces: ${error.message}`,
      };
    }
  }

  private async verifyWorkspaceFiles(): Promise<VerificationResult> {
    try {
      const result = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.files,
        undefined,
        10
      );

      let status: 'success' | 'warning' | 'error' = 'success';
      let message = `Found ${result.total} workspace files`;

      if (result.total === 0) {
        status = 'warning';
        message = 'No workspace files found. Files are seeded automatically with workspaces.';
      }

      return {
        collection: 'Workspace Files',
        count: result.total,
        status,
        message,
        samples: result.documents.slice(0, 3).map(doc => ({
          name: doc.name,
          path: doc.path,
          type: doc.type,
          language: doc.language,
          size: doc.size,
        })),
      };

    } catch (error: any) {
      return {
        collection: 'Workspace Files',
        count: 0,
        status: 'warning',
        message: `Could not verify workspace files: ${error.message}`,
      };
    }
  }

  private async verifyWorkspaceStats(): Promise<VerificationResult> {
    try {
      const result = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.stats,
        undefined,
        10
      );

      let status: 'success' | 'warning' | 'error' = 'success';
      let message = `Found ${result.total} workspace statistics records`;

      if (result.total === 0) {
        status = 'warning';
        message = 'No workspace statistics found. Stats are seeded automatically with workspaces.';
      }

      return {
        collection: 'Workspace Statistics',
        count: result.total,
        status,
        message,
        samples: result.documents.slice(0, 3).map(doc => ({
          workspaceId: doc.workspaceId,
          totalUptime: doc.totalUptime,
          totalSessions: doc.totalSessions,
          averageCpuUsage: doc.averageCpuUsage,
          storageUsed: doc.storageUsed,
        })),
      };

    } catch (error: any) {
      return {
        collection: 'Workspace Statistics',
        count: 0,
        status: 'warning',
        message: `Could not verify workspace statistics: ${error.message}`,
      };
    }
  }

  private displayResults(results: VerificationResult[]): void {
    console.log('📊 Verification Results:\n');

    results.forEach(result => {
      const icon = result.status === 'success' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
      console.log(`${icon} ${result.collection}: ${result.message}`);
      
      if (result.samples && result.samples.length > 0) {
        console.log('   Sample data:');
        result.samples.forEach((sample, index) => {
          const sampleStr = Object.entries(sample)
            .map(([key, value]) => `${key}: ${value}`)
            .join(', ');
          console.log(`   ${index + 1}. ${sampleStr}`);
        });
      }
      console.log();
    });

    // Summary
    const totalCounts = results.reduce((acc, result) => acc + result.count, 0);
    console.log(`📈 Total seeded documents: ${totalCounts}`);
  }
}

// Main execution
async function main() {
  const verifier = new WorkspaceSeederVerifier();
  await verifier.verify();
}

// Run the verifier
if (require.main === module) {
  main().catch(console.error);
}
