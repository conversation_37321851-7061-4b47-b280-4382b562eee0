'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Workspace, WorkspaceFile } from '@/types/workspace';

interface UseWorkspaceInterfaceProps {
  workspaceId: string;
}

interface WorkspaceInterfaceState {
  workspace: Workspace | null;
  files: WorkspaceFile[];
  isLoading: boolean;
  error: string | null;
}

interface FileOperations {
  openFile: (filePath: string) => Promise<WorkspaceFile | null>;
  saveFile: (filePath: string, content: string) => Promise<boolean>;
  createFile: (path: string, name: string, type: 'file' | 'directory') => Promise<boolean>;
  deleteFile: (filePath: string) => Promise<boolean>;
  renameFile: (oldPath: string, newPath: string) => Promise<boolean>;
}

interface WorkspaceOperations {
  startWorkspace: () => Promise<boolean>;
  stopWorkspace: () => Promise<boolean>;
  restartWorkspace: () => Promise<boolean>;
  updateWorkspace: (updates: Partial<Workspace>) => Promise<boolean>;
}

export function useWorkspaceInterface({ workspaceId }: UseWorkspaceInterfaceProps) {
  const { user, isAuthenticated } = useAuth();
  const [state, setState] = useState<WorkspaceInterfaceState>({
    workspace: null,
    files: [],
    isLoading: true,
    error: null,
  });

  // Get authentication headers
  const getAuthHeaders = useCallback(() => {
    const sessionToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('appwrite-session='))
      ?.split('=')[1];

    return {
      'Content-Type': 'application/json',
      ...(sessionToken && { 'x-appwrite-session': sessionToken }),
    };
  }, []);

  // API request helper
  const apiRequest = useCallback(async (endpoint: string, options: RequestInit = {}) => {
    const response = await fetch(endpoint, {
      ...options,
      headers: {
        ...getAuthHeaders(),
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || `Request failed: ${response.statusText}`);
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error?.message || 'Request failed');
    }

    return result.data;
  }, [getAuthHeaders]);

  // Load workspace data
  const loadWorkspace = useCallback(async () => {
    if (!workspaceId || !isAuthenticated || !user) return;

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const workspace = await apiRequest(`/api/workspace/${workspaceId}`);
      setState(prev => ({ ...prev, workspace, isLoading: false }));
    } catch (error) {
      console.error('Error loading workspace:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load workspace',
        isLoading: false,
      }));
    }
  }, [workspaceId, isAuthenticated, user, apiRequest]);

  // Load workspace files
  const loadFiles = useCallback(async () => {
    if (!workspaceId || !isAuthenticated || !user) return;

    try {
      const files = await apiRequest(`/api/workspace/${workspaceId}/files`);
      setState(prev => ({ ...prev, files }));
    } catch (error) {
      console.error('Error loading files:', error);
      // Don't set error state for files, as workspace might still be usable
    }
  }, [workspaceId, isAuthenticated, user, apiRequest]);

  // File operations
  const fileOperations: FileOperations = {
    openFile: useCallback(async (filePath: string) => {
      try {
        const file = await apiRequest(`/api/workspace/${workspaceId}/files/${encodeURIComponent(filePath)}`);
        return file;
      } catch (error) {
        console.error('Error opening file:', error);
        return null;
      }
    }, [workspaceId, apiRequest]),

    saveFile: useCallback(async (filePath: string, content: string) => {
      try {
        await apiRequest(`/api/workspace/${workspaceId}/files/${encodeURIComponent(filePath)}`, {
          method: 'PUT',
          body: JSON.stringify({ content }),
        });
        return true;
      } catch (error) {
        console.error('Error saving file:', error);
        return false;
      }
    }, [workspaceId, apiRequest]),

    createFile: useCallback(async (path: string, name: string, type: 'file' | 'directory') => {
      try {
        await apiRequest(`/api/workspace/${workspaceId}/files`, {
          method: 'POST',
          body: JSON.stringify({
            name,
            path: `${path}/${name}`,
            type,
            content: type === 'file' ? '' : undefined,
          }),
        });
        await loadFiles(); // Refresh file list
        return true;
      } catch (error) {
        console.error('Error creating file:', error);
        return false;
      }
    }, [workspaceId, apiRequest, loadFiles]),

    deleteFile: useCallback(async (filePath: string) => {
      try {
        await apiRequest(`/api/workspace/${workspaceId}/files/${encodeURIComponent(filePath)}`, {
          method: 'DELETE',
        });
        await loadFiles(); // Refresh file list
        return true;
      } catch (error) {
        console.error('Error deleting file:', error);
        return false;
      }
    }, [workspaceId, apiRequest, loadFiles]),

    renameFile: useCallback(async (oldPath: string, newPath: string) => {
      try {
        await apiRequest(`/api/workspace/${workspaceId}/files/${encodeURIComponent(oldPath)}/rename`, {
          method: 'POST',
          body: JSON.stringify({ newPath }),
        });
        await loadFiles(); // Refresh file list
        return true;
      } catch (error) {
        console.error('Error renaming file:', error);
        return false;
      }
    }, [workspaceId, apiRequest, loadFiles]),
  };

  // Workspace operations
  const workspaceOperations: WorkspaceOperations = {
    startWorkspace: useCallback(async () => {
      try {
        await apiRequest(`/api/workspace/${workspaceId}/start`, {
          method: 'POST',
        });
        await loadWorkspace(); // Refresh workspace data
        return true;
      } catch (error) {
        console.error('Error starting workspace:', error);
        return false;
      }
    }, [workspaceId, apiRequest, loadWorkspace]),

    stopWorkspace: useCallback(async () => {
      try {
        await apiRequest(`/api/workspace/${workspaceId}/stop`, {
          method: 'POST',
        });
        await loadWorkspace(); // Refresh workspace data
        return true;
      } catch (error) {
        console.error('Error stopping workspace:', error);
        return false;
      }
    }, [workspaceId, apiRequest, loadWorkspace]),

    restartWorkspace: useCallback(async () => {
      try {
        await apiRequest(`/api/workspace/${workspaceId}/restart`, {
          method: 'POST',
        });
        await loadWorkspace(); // Refresh workspace data
        return true;
      } catch (error) {
        console.error('Error restarting workspace:', error);
        return false;
      }
    }, [workspaceId, apiRequest, loadWorkspace]),

    updateWorkspace: useCallback(async (updates: Partial<Workspace>) => {
      try {
        const updatedWorkspace = await apiRequest(`/api/workspace/${workspaceId}`, {
          method: 'PUT',
          body: JSON.stringify({ ...updates, workspaceId }),
        });
        setState(prev => ({ ...prev, workspace: updatedWorkspace }));
        return true;
      } catch (error) {
        console.error('Error updating workspace:', error);
        return false;
      }
    }, [workspaceId, apiRequest]),
  };

  // Load data on mount and when dependencies change
  useEffect(() => {
    loadWorkspace();
  }, [loadWorkspace]);

  useEffect(() => {
    if (state.workspace) {
      loadFiles();
    }
  }, [state.workspace, loadFiles]);

  // Refresh data periodically for active workspaces
  useEffect(() => {
    if (state.workspace?.status === 'active') {
      const interval = setInterval(() => {
        loadWorkspace();
      }, 30000); // Refresh every 30 seconds

      return () => clearInterval(interval);
    }
  }, [state.workspace?.status, loadWorkspace]);

  return {
    ...state,
    fileOperations,
    workspaceOperations,
    refresh: loadWorkspace,
    refreshFiles: loadFiles,
  };
}
