/* XTerm.js Terminal Styles */

.xterm-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.xterm-container .xterm {
  width: 100% !important;
  height: 100% !important;
}

.xterm-container .xterm-viewport {
  overflow-y: auto !important;
}

.xterm-container .xterm-screen {
  width: 100% !important;
  height: 100% !important;
}

/* Terminal Layout Styles */
.terminal-layout {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.terminal-container {
  display: flex;
  flex-direction: column;
  position: relative;
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  overflow: hidden;
}

.terminal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  border-bottom: 1px solid var(--border);
  background: var(--muted);
  flex-shrink: 0;
}

.terminal-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.terminal-footer {
  display: flex;
  align-items: center;
  justify-content: between;
  padding: 0.25rem 0.5rem;
  border-top: 1px solid var(--border);
  background: var(--muted);
  font-size: 0.75rem;
  flex-shrink: 0;
}

/* Terminal Theme Overrides */
.xterm-container .xterm-decoration-overview-ruler {
  display: none;
}

.xterm-container .xterm-decoration-top {
  display: none;
}

/* Custom Scrollbar for Terminal */
.xterm-container .xterm-viewport::-webkit-scrollbar {
  width: 8px;
}

.xterm-container .xterm-viewport::-webkit-scrollbar-track {
  background: transparent;
}

.xterm-container .xterm-viewport::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
}

.xterm-container .xterm-viewport::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Terminal Tab Styles */
.terminal-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  border-right: 1px solid var(--border);
}

.terminal-tab:hover {
  background: var(--muted);
}

.terminal-tab.active {
  background: var(--background);
  border-bottom-color: var(--primary);
}

.terminal-tab-close {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.terminal-tab:hover .terminal-tab-close {
  opacity: 1;
}

/* Multi-Terminal Layout Styles */
.multi-terminal-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.multi-terminal-tabs {
  display: flex;
  border-bottom: 1px solid var(--border);
  background: var(--muted);
  overflow-x: auto;
  flex-shrink: 0;
}

.multi-terminal-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.multi-terminal-grid {
  display: grid;
  width: 100%;
  height: 100%;
  gap: 1px;
  background: var(--border);
}

.multi-terminal-split-horizontal {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 1px;
  background: var(--border);
}

.multi-terminal-split-vertical {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  gap: 1px;
  background: var(--border);
}

/* Terminal Status Indicators */
.terminal-status {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
}

.terminal-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.terminal-status-indicator.connected {
  background: var(--success);
  animation: pulse 2s infinite;
}

.terminal-status-indicator.disconnected {
  background: var(--destructive);
}

.terminal-status-indicator.connecting {
  background: var(--warning);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Terminal Toolbar Styles */
.terminal-toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-bottom: 1px solid var(--border);
  background: var(--muted);
  flex-wrap: wrap;
}

.terminal-toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.terminal-toolbar-separator {
  width: 1px;
  height: 1rem;
  background: var(--border);
  margin: 0 0.5rem;
}

/* Responsive Terminal Styles */
@media (max-width: 768px) {
  .terminal-header {
    padding: 0.25rem;
    font-size: 0.875rem;
  }
  
  .terminal-toolbar {
    padding: 0.25rem;
    gap: 0.25rem;
  }
  
  .terminal-tab {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
  
  .multi-terminal-tabs {
    overflow-x: scroll;
  }
}

/* Terminal Loading Overlay */
.terminal-loading-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background);
  z-index: 10;
}

.terminal-loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Terminal Fullscreen Styles */
.terminal-fullscreen {
  position: fixed;
  inset: 0;
  z-index: 50;
  background: var(--background);
}

.terminal-fullscreen .terminal-container {
  height: 100vh;
  border: none;
  border-radius: 0;
}

/* Terminal Selection Styles */
.xterm-container .xterm-selection {
  background: var(--primary) !important;
  opacity: 0.3 !important;
}

/* Terminal Search Highlight */
.xterm-container .xterm-decoration-overview-ruler {
  display: none;
}

/* Custom Terminal Cursors */
.xterm-container .xterm-cursor-blink {
  animation: terminal-cursor-blink 1s infinite;
}

@keyframes terminal-cursor-blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* Terminal Context Menu */
.terminal-context-menu {
  position: fixed;
  background: var(--popover);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 0.25rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
}

.terminal-context-menu-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: calc(var(--radius) - 2px);
  font-size: 0.875rem;
  cursor: pointer;
  transition: background 0.1s ease;
}

.terminal-context-menu-item:hover {
  background: var(--accent);
}

.terminal-context-menu-separator {
  height: 1px;
  background: var(--border);
  margin: 0.25rem 0;
}

/* Print styles for terminal content */
@media print {
  .terminal-header,
  .terminal-footer,
  .terminal-toolbar {
    display: none;
  }
  
  .terminal-container {
    border: none;
    box-shadow: none;
  }
  
  .xterm-container {
    background: white;
    color: black;
  }
}