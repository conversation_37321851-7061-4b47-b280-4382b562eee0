# XTerm.js Integration Documentation

## Overview

This documentation covers the comprehensive XTerm.js integration implemented in the omnispace platform, providing web-based terminal functionality for local, SSH, and Docker environments.

## Architecture

### Core Components

1. **BaseTerminal** (`src/components/terminal/base-terminal.tsx`)
   - Foundation terminal component using XTerm.js
   - Handles all XTerm.js addons and configuration
   - Provides standardized interface for all terminal types

2. **SSHTerminal** (`src/components/terminal/ssh-terminal.tsx`)
   - SSH-specific terminal implementation
   - Integrates with SSH connection management
   - Provides SSH-specific UI controls and status

3. **DockerTerminal** (`src/components/terminal/docker-terminal.tsx`)
   - Docker container terminal implementation
   - Container selection and lifecycle integration
   - Docker-specific commands and monitoring

4. **MultiTerminal** (`src/components/terminal/multi-terminal.tsx`)
   - Advanced multi-session terminal manager
   - Supports tabs, splits, and grid layouts
   - Unified interface for all terminal types

### Dashboard Pages

1. **Terminal Dashboard** (`/dashboard/terminals`)
   - Central hub for all terminal functionality
   - Quick start templates and monitoring
   - Multi-tab interface for different terminal types

2. **SSH Terminal Page** (`/dashboard/ssh-terminal`)
   - Dedicated SSH terminal management
   - Connection configuration and monitoring
   - Multi-session SSH terminal environment

3. **Docker Terminal Page** (`/dashboard/docker-terminal`)
   - Container-focused terminal interface
   - Real-time container monitoring
   - Multi-container terminal management

## XTerm.js Package Configuration

### Installed Packages

```json
{
  "@xterm/xterm": "^5.5.0",
  "@xterm/addon-attach": "^0.11.0",
  "@xterm/addon-fit": "^0.10.0",
  "@xterm/addon-search": "^0.16.0",
  "@xterm/addon-web-links": "^0.11.0",
  "@xterm/addon-webgl": "^0.18.0"
}
```

### Addons Used

- **FitAddon**: Automatically resizes terminal to container
- **WebLinksAddon**: Makes URLs clickable in terminal
- **SearchAddon**: Text search functionality
- **AttachAddon**: WebSocket attachment for real-time communication
- **WebglAddon**: Hardware-accelerated rendering (optional)

## Features

### BaseTerminal Features

- **Multiple Themes**: Default, Dark, Light, Solarized Dark
- **Customizable Fonts**: Font family and size configuration
- **Advanced Rendering**: Canvas and WebGL rendering options
- **Search Functionality**: Find and highlight text
- **Copy/Paste Support**: Clipboard integration
- **Auto-fit**: Automatic terminal resizing
- **Scrollback Buffer**: Configurable history length

### Terminal Themes

```typescript
const TERMINAL_THEMES = {
  default: { /* Standard dark theme */ },
  dark: { /* Dracula-inspired theme */ },
  light: { /* Light theme for bright environments */ },
  solarizedDark: { /* Solarized dark theme */ },
};
```

### Responsive Design

- **Auto-fitting**: Terminals automatically resize with container
- **Fullscreen Mode**: Expandable terminal interface
- **Mobile-friendly**: Touch-optimized controls
- **Accessibility**: Screen reader and keyboard navigation support

## Usage Examples

### Basic Terminal

```tsx
import { BaseTerminal, BaseTerminalRef } from '@/components/terminal/base-terminal';

function MyTerminal() {
  const terminalRef = useRef<BaseTerminalRef>(null);

  return (
    <BaseTerminal
      ref={terminalRef}
      height="400px"
      options={{ theme: 'dark', fontSize: 14 }}
      onData={(data) => console.log('User input:', data)}
      enableWebLinks
      enableSearch
      enableFit
    />
  );
}
```

### SSH Terminal

```tsx
import { SSHTerminal } from '@/components/terminal/ssh-terminal';

function MySSHTerminal() {
  return (
    <SSHTerminal
      connectionConfig={{
        id: 'server-1',
        name: 'Production Server',
        host: '*************',
        username: 'admin',
        authentication: { type: 'privateKey', privateKey: '/path/to/key' }
      }}
      height="600px"
      autoConnect
      showToolbar
      showStatusBar
    />
  );
}
```

### Docker Terminal

```tsx
import { DockerTerminal } from '@/components/terminal/docker-terminal';

function MyDockerTerminal() {
  return (
    <DockerTerminal
      containerId="container-123"
      height="500px"
      defaultShell="/bin/bash"
      autoConnect
      showToolbar
      showStatusBar
    />
  );
}
```

### Multi-Terminal

```tsx
import { MultiTerminal } from '@/components/terminal/multi-terminal';

function MyMultiTerminal() {
  return (
    <MultiTerminal
      height="700px"
      maxTabs={10}
      allowSplit
      allowReorder
      defaultTabs={[
        { type: 'local', title: 'Main Terminal' },
        { type: 'ssh', title: 'Server 1', config: { connectionId: 'server-1' } },
        { type: 'docker', title: 'Container', config: { containerId: 'app-1' } }
      ]}
    />
  );
}
```

## WebSocket Integration

### Terminal Communication

Terminals communicate with backend services via WebSocket connections:

- **SSH Terminals**: `/api/ssh-terminal/[connectionId]`
- **Docker Terminals**: `/api/docker/containers/[id]/exec`
- **Local Terminals**: Local WebSocket or Server-Sent Events

### Data Flow

1. **User Input**: XTerm captures keyboard/mouse input
2. **WebSocket Send**: Input sent to backend via WebSocket
3. **Backend Processing**: Backend executes commands (SSH/Docker/Local)
4. **Output Stream**: Command output streamed back via WebSocket
5. **Terminal Display**: XTerm displays output in real-time

## Advanced Features

### Terminal Sessions

- **Session Persistence**: Reconnect to existing sessions
- **Session Sharing**: Share terminal sessions between users
- **Session Recording**: Record and replay terminal sessions

### Multi-Layout Support

- **Tabs**: Traditional tabbed interface
- **Horizontal Split**: Side-by-side terminals
- **Vertical Split**: Stacked terminals
- **Grid Layout**: Multiple terminals in grid formation

### Customization

- **Themes**: Multiple built-in themes with custom theme support
- **Fonts**: Font family and size customization
- **Keybindings**: Custom keyboard shortcuts
- **Context Menus**: Right-click context menus

## Performance Optimization

### Rendering Performance

- **WebGL Acceleration**: Hardware-accelerated rendering when available
- **Canvas Fallback**: Reliable canvas rendering for compatibility
- **Efficient Scrolling**: Optimized scrollback buffer management

### Memory Management

- **Connection Pooling**: Reuse WebSocket connections
- **Buffer Limits**: Configurable scrollback limits
- **Cleanup**: Proper resource cleanup on component unmount

### Network Optimization

- **Connection Compression**: WebSocket compression when supported
- **Reconnection Logic**: Automatic reconnection on network issues
- **Heartbeat**: Keep-alive mechanisms for long-running sessions

## Security Considerations

### Input Sanitization

- **XSS Prevention**: Escape sequences properly handled
- **Command Injection**: Input validation on backend
- **Buffer Overflow**: Controlled buffer sizes

### Authentication

- **Session Tokens**: Secure WebSocket authentication
- **SSH Key Management**: Secure key storage and handling
- **Docker Security**: Container isolation and permissions

### Audit Logging

- **Session Logging**: Record terminal sessions for audit
- **Command Logging**: Log executed commands
- **Access Logging**: Track terminal access patterns

## Troubleshooting

### Common Issues

1. **Terminal Not Appearing**
   - Check CSS imports for XTerm.js
   - Verify container dimensions
   - Ensure proper React refs

2. **WebSocket Connection Fails**
   - Verify WebSocket server is running
   - Check authentication headers
   - Confirm network connectivity

3. **Performance Issues**
   - Disable WebGL if causing problems
   - Reduce scrollback buffer size
   - Check for memory leaks

4. **Rendering Problems**
   - Try different renderer types
   - Check browser compatibility
   - Verify theme configuration

### Debug Mode

Enable debug logging:

```typescript
// In terminal components
const DEBUG = process.env.NODE_ENV === 'development';

if (DEBUG) {
  terminal.onData(data => console.log('Terminal data:', data));
  terminal.onRender(e => console.log('Terminal render:', e));
}
```

## Browser Compatibility

### Supported Browsers

- **Chrome**: 80+ (full feature support)
- **Firefox**: 75+ (full feature support)
- **Safari**: 13+ (limited WebGL support)
- **Edge**: 80+ (full feature support)

### Feature Detection

```typescript
// Check WebGL support
const hasWebGL = () => {
  try {
    const canvas = document.createElement('canvas');
    return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
  } catch (e) {
    return false;
  }
};

// Use appropriate renderer
const rendererType = hasWebGL() ? 'webgl' : 'canvas';
```

## Future Enhancements

### Planned Features

- **File Transfer**: Drag-and-drop file upload/download
- **Terminal Sharing**: Real-time collaborative terminals
- **Plugin System**: Custom terminal extensions
- **AI Integration**: AI-powered command suggestions
- **Mobile App**: React Native terminal implementation

### Performance Improvements

- **Virtual Scrolling**: Handle extremely large outputs
- **Background Processing**: Non-blocking terminal operations
- **Caching**: Intelligent session and output caching
- **Compression**: Advanced data compression algorithms

This XTerm.js integration provides a comprehensive, production-ready terminal solution with advanced features, excellent performance, and robust security for web-based terminal access.# XTerm.js Integration Documentation

## Overview

This documentation covers the comprehensive XTerm.js integration implemented in the omnispace platform, providing web-based terminal functionality for local, SSH, and Docker environments.

## Architecture

### Core Components

1. **BaseTerminal** (`src/components/terminal/base-terminal.tsx`)
   - Foundation terminal component using XTerm.js
   - Handles all XTerm.js addons and configuration
   - Provides standardized interface for all terminal types

2. **SSHTerminal** (`src/components/terminal/ssh-terminal.tsx`)
   - SSH-specific terminal implementation
   - Integrates with SSH connection management
   - Provides SSH-specific UI controls and status

3. **DockerTerminal** (`src/components/terminal/docker-terminal.tsx`)
   - Docker container terminal implementation
   - Container selection and lifecycle integration
   - Docker-specific commands and monitoring

4. **MultiTerminal** (`src/components/terminal/multi-terminal.tsx`)
   - Advanced multi-session terminal manager
   - Supports tabs, splits, and grid layouts
   - Unified interface for all terminal types

### Dashboard Pages

1. **Terminal Dashboard** (`/dashboard/terminals`)
   - Central hub for all terminal functionality
   - Quick start templates and monitoring
   - Multi-tab interface for different terminal types

2. **SSH Terminal Page** (`/dashboard/ssh-terminal`)
   - Dedicated SSH terminal management
   - Connection configuration and monitoring
   - Multi-session SSH terminal environment

3. **Docker Terminal Page** (`/dashboard/docker-terminal`)
   - Container-focused terminal interface
   - Real-time container monitoring
   - Multi-container terminal management

## XTerm.js Package Configuration

### Installed Packages

```json
{
  "@xterm/xterm": "^5.5.0",
  "@xterm/addon-attach": "^0.11.0",
  "@xterm/addon-fit": "^0.10.0",
  "@xterm/addon-search": "^0.16.0",
  "@xterm/addon-web-links": "^0.11.0",
  "@xterm/addon-webgl": "^0.18.0"
}
```

### Addons Used

- **FitAddon**: Automatically resizes terminal to container
- **WebLinksAddon**: Makes URLs clickable in terminal
- **SearchAddon**: Text search functionality
- **AttachAddon**: WebSocket attachment for real-time communication
- **WebglAddon**: Hardware-accelerated rendering (optional)

## Features

### BaseTerminal Features

- **Multiple Themes**: Default, Dark, Light, Solarized Dark
- **Customizable Fonts**: Font family and size configuration
- **Advanced Rendering**: Canvas and WebGL rendering options
- **Search Functionality**: Find and highlight text
- **Copy/Paste Support**: Clipboard integration
- **Auto-fit**: Automatic terminal resizing
- **Scrollback Buffer**: Configurable history length

### Terminal Themes

```typescript
const TERMINAL_THEMES = {
  default: { /* Standard dark theme */ },
  dark: { /* Dracula-inspired theme */ },
  light: { /* Light theme for bright environments */ },
  solarizedDark: { /* Solarized dark theme */ },
};
```

### Responsive Design

- **Auto-fitting**: Terminals automatically resize with container
- **Fullscreen Mode**: Expandable terminal interface
- **Mobile-friendly**: Touch-optimized controls
- **Accessibility**: Screen reader and keyboard navigation support

## Usage Examples

### Basic Terminal

```tsx
import { BaseTerminal, BaseTerminalRef } from '@/components/terminal/base-terminal';

function MyTerminal() {
  const terminalRef = useRef<BaseTerminalRef>(null);

  return (
    <BaseTerminal
      ref={terminalRef}
      height="400px"
      options={{ theme: 'dark', fontSize: 14 }}
      onData={(data) => console.log('User input:', data)}
      enableWebLinks
      enableSearch
      enableFit
    />
  );
}
```

### SSH Terminal

```tsx
import { SSHTerminal } from '@/components/terminal/ssh-terminal';

function MySSHTerminal() {
  return (
    <SSHTerminal
      connectionConfig={{
        id: 'server-1',
        name: 'Production Server',
        host: '*************',
        username: 'admin',
        authentication: { type: 'privateKey', privateKey: '/path/to/key' }
      }}
      height="600px"
      autoConnect
      showToolbar
      showStatusBar
    />
  );
}
```

### Docker Terminal

```tsx
import { DockerTerminal } from '@/components/terminal/docker-terminal';

function MyDockerTerminal() {
  return (
    <DockerTerminal
      containerId="container-123"
      height="500px"
      defaultShell="/bin/bash"
      autoConnect
      showToolbar
      showStatusBar
    />
  );
}
```

### Multi-Terminal

```tsx
import { MultiTerminal } from '@/components/terminal/multi-terminal';

function MyMultiTerminal() {
  return (
    <MultiTerminal
      height="700px"
      maxTabs={10}
      allowSplit
      allowReorder
      defaultTabs={[
        { type: 'local', title: 'Main Terminal' },
        { type: 'ssh', title: 'Server 1', config: { connectionId: 'server-1' } },
        { type: 'docker', title: 'Container', config: { containerId: 'app-1' } }
      ]}
    />
  );
}
```

## WebSocket Integration

### Terminal Communication

Terminals communicate with backend services via WebSocket connections:

- **SSH Terminals**: `/api/ssh-terminal/[connectionId]`
- **Docker Terminals**: `/api/docker/containers/[id]/exec`
- **Local Terminals**: Local WebSocket or Server-Sent Events

### Data Flow

1. **User Input**: XTerm captures keyboard/mouse input
2. **WebSocket Send**: Input sent to backend via WebSocket
3. **Backend Processing**: Backend executes commands (SSH/Docker/Local)
4. **Output Stream**: Command output streamed back via WebSocket
5. **Terminal Display**: XTerm displays output in real-time

## Advanced Features

### Terminal Sessions

- **Session Persistence**: Reconnect to existing sessions
- **Session Sharing**: Share terminal sessions between users
- **Session Recording**: Record and replay terminal sessions

### Multi-Layout Support

- **Tabs**: Traditional tabbed interface
- **Horizontal Split**: Side-by-side terminals
- **Vertical Split**: Stacked terminals
- **Grid Layout**: Multiple terminals in grid formation

### Customization

- **Themes**: Multiple built-in themes with custom theme support
- **Fonts**: Font family and size customization
- **Keybindings**: Custom keyboard shortcuts
- **Context Menus**: Right-click context menus

## Performance Optimization

### Rendering Performance

- **WebGL Acceleration**: Hardware-accelerated rendering when available
- **Canvas Fallback**: Reliable canvas rendering for compatibility
- **Efficient Scrolling**: Optimized scrollback buffer management

### Memory Management

- **Connection Pooling**: Reuse WebSocket connections
- **Buffer Limits**: Configurable scrollback limits
- **Cleanup**: Proper resource cleanup on component unmount

### Network Optimization

- **Connection Compression**: WebSocket compression when supported
- **Reconnection Logic**: Automatic reconnection on network issues
- **Heartbeat**: Keep-alive mechanisms for long-running sessions

## Security Considerations

### Input Sanitization

- **XSS Prevention**: Escape sequences properly handled
- **Command Injection**: Input validation on backend
- **Buffer Overflow**: Controlled buffer sizes

### Authentication

- **Session Tokens**: Secure WebSocket authentication
- **SSH Key Management**: Secure key storage and handling
- **Docker Security**: Container isolation and permissions

### Audit Logging

- **Session Logging**: Record terminal sessions for audit
- **Command Logging**: Log executed commands
- **Access Logging**: Track terminal access patterns

## Troubleshooting

### Common Issues

1. **Terminal Not Appearing**
   - Check CSS imports for XTerm.js
   - Verify container dimensions
   - Ensure proper React refs

2. **WebSocket Connection Fails**
   - Verify WebSocket server is running
   - Check authentication headers
   - Confirm network connectivity

3. **Performance Issues**
   - Disable WebGL if causing problems
   - Reduce scrollback buffer size
   - Check for memory leaks

4. **Rendering Problems**
   - Try different renderer types
   - Check browser compatibility
   - Verify theme configuration

### Debug Mode

Enable debug logging:

```typescript
// In terminal components
const DEBUG = process.env.NODE_ENV === 'development';

if (DEBUG) {
  terminal.onData(data => console.log('Terminal data:', data));
  terminal.onRender(e => console.log('Terminal render:', e));
}
```

## Browser Compatibility

### Supported Browsers

- **Chrome**: 80+ (full feature support)
- **Firefox**: 75+ (full feature support)
- **Safari**: 13+ (limited WebGL support)
- **Edge**: 80+ (full feature support)

### Feature Detection

```typescript
// Check WebGL support
const hasWebGL = () => {
  try {
    const canvas = document.createElement('canvas');
    return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
  } catch (e) {
    return false;
  }
};

// Use appropriate renderer
const rendererType = hasWebGL() ? 'webgl' : 'canvas';
```

## Future Enhancements

### Planned Features

- **File Transfer**: Drag-and-drop file upload/download
- **Terminal Sharing**: Real-time collaborative terminals
- **Plugin System**: Custom terminal extensions
- **AI Integration**: AI-powered command suggestions
- **Mobile App**: React Native terminal implementation

### Performance Improvements

- **Virtual Scrolling**: Handle extremely large outputs
- **Background Processing**: Non-blocking terminal operations
- **Caching**: Intelligent session and output caching
- **Compression**: Advanced data compression algorithms

This XTerm.js integration provides a comprehensive, production-ready terminal solution with advanced features, excellent performance, and robust security for web-based terminal access.