# Workspace Seeding Guide

This guide explains how to seed your Omnispace platform with realistic workspace data for development, testing, and demonstration purposes.

## 🌱 Overview

The workspace seeding system provides:
- **Workspace Templates**: Pre-configured templates for different technology stacks
- **Sample Workspaces**: Realistic workspaces with various statuses and configurations
- **File Structures**: Sample files and directory structures for each workspace
- **Statistics**: Usage statistics and performance metrics
- **Collaboration Data**: Sample collaboration and permission data

## 📋 Prerequisites

Before running the seeding scripts, ensure you have:

1. **Appwrite Setup**: Complete Appwrite configuration
2. **Environment Variables**: Properly configured `.env.local` file
3. **Database Collections**: All required collections created
4. **User Data**: At least one user account (run `pnpm seed:appwrite` first)

## 🚀 Quick Start

### Seed Everything
```bash
# Seed all data (users, templates, workspaces)
pnpm seed:all
```

### Individual Seeding
```bash
# Seed workspace templates only
pnpm seed:templates

# Seed workspaces only (requires existing users)
pnpm seed:workspaces

# Seed basic user data first
pnpm seed:appwrite
```

## 📊 What Gets Seeded

### 1. Workspace Templates

**Python Templates:**
- **Python FastAPI Starter**: Modern API development with FastAPI, SQLAlchemy
- **Django Web Application**: Full-stack Django with authentication and admin
- **Machine Learning Workspace**: Jupyter, TensorFlow, PyTorch environment
- **Data Science Environment**: Pandas, NumPy, Matplotlib, Seaborn setup

**Node.js Templates:**
- **Next.js TypeScript App**: Modern React with TypeScript and Tailwind
- **Express API Server**: RESTful API with authentication and middleware
- **React Native Mobile**: Cross-platform mobile development with Expo
- **Vue.js SPA**: Single-page application with Vue 3 and Composition API

**General Templates:**
- **DevOps Infrastructure**: Terraform, Docker, Kubernetes configurations
- **Full-Stack MERN**: MongoDB, Express, React, Node.js complete stack
- **Microservices Architecture**: Multi-service setup with Docker Compose

### 2. Sample Workspaces

**Active Workspaces:**
- **E-commerce Platform Development**: Django-based e-commerce with payment integration
- **Next.js Video Conferencing System**: WebRTC-based video conferencing platform
- **Machine Learning Research Project**: Computer vision research with PyTorch

**Development Workspaces:**
- **React Native Mobile App**: Cross-platform mobile application
- **DevOps Infrastructure Setup**: Infrastructure as Code implementation

**Archived/Stopped Workspaces:**
- **Legacy PHP Application**: Maintenance mode workspace
- **Prototype Testing Environment**: Experimental features workspace

### 3. File Structures

Each workspace includes realistic file structures:
- **Source Code**: Sample application files with proper syntax
- **Configuration Files**: Package.json, requirements.txt, docker files
- **Documentation**: README files, API documentation
- **Tests**: Unit tests and integration test examples
- **Build Files**: Webpack, Vite, or other build configurations

### 4. Workspace Statistics

Realistic usage statistics including:
- **Uptime Metrics**: Total uptime, session duration
- **Resource Usage**: CPU, memory, storage utilization
- **Activity Data**: Commands executed, files modified
- **Performance Metrics**: Network usage, peak resource consumption

## 🔧 Configuration

### Environment Variables

Ensure these variables are set in your `.env.local`:

```env
# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://your-appwrite-endpoint
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your-project-id
APPWRITE_API_KEY=your-api-key

# Database Configuration
NEXT_PUBLIC_APPWRITE_DATABASE_ID=omnispace_db
NEXT_PUBLIC_APPWRITE_WORKSPACES_COLLECTION_ID=workspaces
```

### Collection Requirements

The seeding scripts require these collections:
- `workspaces`: Main workspace data
- `workspace-templates`: Template definitions
- `workspace-files`: File system data
- `workspace-stats`: Usage statistics
- `workspace-permissions`: Access control data

## 📁 Seeded Data Structure

### Workspace Templates
```typescript
interface WorkspaceTemplate {
  name: string;
  description: string;
  type: 'python' | 'nodejs' | 'general' | 'collaborative';
  category: string;
  configuration: {
    runtime: { version: string; environment: object; dependencies: string[] };
    resources: { cpu: number; memory: number; storage: number };
    editor: { theme: string; fontSize: number; tabSize: number };
  };
  files: TemplateFile[];
  structure: DirectoryStructure;
  requirements: { minCpu: number; minMemory: number; dependencies: string[] };
  usageCount: number;
  rating: number;
  featured: boolean;
}
```

### Sample Workspaces
```typescript
interface Workspace {
  name: string;
  description: string;
  type: WorkspaceType;
  status: 'creating' | 'active' | 'stopped' | 'error' | 'archived';
  visibility: 'private' | 'team' | 'public';
  ownerId: string;
  configuration: WorkspaceConfiguration;
  tags: string[];
  aiEnabled: boolean;
  fileCount: number;
  totalSize: number;
}
```

## 🎯 Use Cases

### Development
- **Local Testing**: Realistic data for feature development
- **UI/UX Testing**: Various workspace states and configurations
- **Performance Testing**: Different resource usage patterns

### Demonstration
- **Product Demos**: Showcase different workspace types
- **Feature Presentations**: Show collaboration and AI features
- **Customer Onboarding**: Example workspaces for new users

### Testing
- **Integration Tests**: Real workspace data for API testing
- **Load Testing**: Multiple workspaces with varying complexity
- **User Experience Testing**: Different user scenarios and workflows

## 🔍 Verification

After seeding, verify the data:

```bash
# Check Appwrite configuration
pnpm diagnose:appwrite

# Verify workspace creation through the UI
# Navigate to /dashboard/workspaces

# Check template availability
# Navigate to workspace creation flow
```

## 🧹 Cleanup

To reset seeded data:

1. **Manual Cleanup**: Delete documents through Appwrite Console
2. **Collection Reset**: Drop and recreate collections
3. **Selective Cleanup**: Remove specific workspace types or templates

## 🚨 Important Notes

- **User Dependencies**: Workspaces require existing users as owners
- **Resource Limits**: Ensure your Appwrite instance can handle the seeded data
- **Development Only**: This seeding is intended for development environments
- **Data Persistence**: Seeded data will persist until manually removed
- **Incremental Seeding**: Scripts check for existing data to avoid duplicates

## 📚 Related Documentation

- [Workspace Management](./WORKSPACE_MANAGEMENT.md)
- [Appwrite Integration](./APPWRITE_INTEGRATION_COMPLETE.md)
- [Development Setup](./DEVELOPMENT_SETUP.md)
- [API Documentation](./API_DOCUMENTATION.md)

## 🤝 Contributing

To add new templates or workspace examples:

1. **Template Definition**: Add to `seed-workspace-templates.ts`
2. **Workspace Examples**: Add to `seed-workspaces.ts`
3. **File Structures**: Include realistic file content
4. **Documentation**: Update this guide with new additions

## 🐛 Troubleshooting

### Common Issues

**Permission Errors:**
- Verify API key has proper permissions
- Check collection-level permissions in Appwrite

**Missing Dependencies:**
- Ensure all required collections exist
- Run `pnpm setup:appwrite` first

**Data Conflicts:**
- Scripts check for existing data
- Use Appwrite Console to resolve conflicts

**Resource Limits:**
- Monitor Appwrite resource usage
- Adjust seeding data size if needed
